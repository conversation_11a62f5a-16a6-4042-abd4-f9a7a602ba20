# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Development

- `bun run dev` - Start development server with Vite
- `bun run build` - Production build
- `bun run lint` - Run ESLint with auto-fix for .js/.tsx/.ts files
- `bun run format` - Run Prettier formatting
- `ddev artisan ` - for any artisan commands

### Testing

- `ddev test` - Run PHP tests (Pest framework)

### Laravel Artisan Commands

- `ddev artisan import:family-tree {filename}` - Import family tree data from JSON
- `ddev artisan reset:demo-data` - Reset demo data
- `ddev artisan detect:cycles` - Detect relationship cycles in family tree
- `ddev artisan fix:misspelled-node-names` - Fix misspelled node names
- `ddev artisan clean:otp` - Clean expired OTP tokens
- `ddev artisan horizon` - Start queue worker (for background jobs)

## Architecture Overview

### Multi-Tenant Family Tree Application

This is "أوراق" (Awraq), a production-grade genealogy platform built with <PERSON><PERSON> 12+ and React 19.

### Core Technology Stack

- **Backend**: Lara<PERSON> 12+ with PHP 8.2+, MySQL, Redis
- **Frontend**: React 19 + TypeScript with Inertia.js (currently migrating from Vue.js)
- **UI**: shadcn/ui components with Radix UI primitives and TailwindCSS 4.0
- **State**: Zustand (reactive-only usage), TanStack Query for server state
- **Visualization**: Konva.js for interactive tree canvas, D3.js for charts

### Multi-Tenancy Architecture

- **Tenant Isolation**: All models use `BelongsToTenant` trait with automatic scoping
- **Subdomain Routing**: Tenants accessed via subdomain (tenant.domain.com)
- **Tenant Resolution**: `ProcessTenantFromDomain` middleware handles tenant context
- **Global Scope**: `TenantScope` automatically filters all queries by tenant_id

### Core Models & Relationships

- **NodeModel**: Central family member entity with recursive parent-child relationships using `staudenmeir/laravel-adjacency-list`
- **Relationship**: Handles marriages/partnerships between nodes
- **Branch**: Organizational structure within family trees
- **Tenant**: Multi-tenant isolation with subdomain-based access
- **User/Member/Guest**: Different access levels (authenticated users, family members, guests)

### Family Tree Features

- **NodeModel Approval Workflow**: NodeAddition/NodeChange models for pending changes
- **Recursive Tree Operations**: Uses adjacency list pattern for efficient tree queries
- **Arabic Language Support**: Hijri calendar integration, RTL text, Arabic name normalization

### Key Services

- **CycleService**: Detects circular relationships in family tree
- **ProcessNode**: Background job processing for node statistics
- **QuickStatisticsQueryService**: Optimized queries for dashboard stats
- **LoginService**: Handles multi-auth (users, members, guests) with OTP

## Development Guidelines

### Frontend (.windsurfrules)

- **Strict TypeScript**: No `any` types, use precise typing
- **Named Exports**: Always use named exports (except Inertia pages)
- **Icons**: Use `@hugeicons-pro/core-stroke-standard` with `HugeiconsIcon` component
- **Styling**: Use `clsx` for conditional classes, shadcn/ui for components
- **Inertia.js**: Use `InertiaFormProps<T>` for form typing, pass form object as prop
- **Zustand**: Only reactive usage through hooks, never `getState()`
- **Production Ready**: No TODOs, placeholders, or partial implementations

### Backend Patterns

- **Multi-Tenancy**: All models extend `BelongsToTenant`, use tenant() helper function
- **Observers**: NodeObserver, RelationshipObserver handle data consistency
- **Validation**: Custom rules `tenant_unique`, `tenant_exists` for tenant-scoped validation
- **Background Jobs**: Use Laravel Horizon for queue processing
- **File Storage**: S3 via Flysystem for user uploads

### Database Patterns

- **Recursive Trees**: Use `HasRecursiveRelationships` trait for hierarchical data
- **Polymorphic Relations**: Flexible associations using `awobaz/compoships`
- **Soft Deletes**: Important models use soft deletes for data preservation
- **Indexes**: Tenant-aware indexes for performance

## Vue.js to React Migration Status

The project is currently undergoing active migration from Vue.js to React. When working on files:

- Convert Vue components to React/TypeScript following .windsurfrules
- Update imports from Vue ecosystem to React equivalents
- Maintain exact functionality and design during conversion
- Use shadcn/ui components instead of custom Vue components where possible
