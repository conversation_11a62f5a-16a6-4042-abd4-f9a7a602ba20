import { exec } from 'child_process';
import fs from 'fs/promises';
import { glob } from 'glob';
import path from 'path';
import { promisify } from 'util';

const execAsync = promisify(exec);

async function convertToWebp() {
  const tempDir = 'resources/js/assets/images-temp';

  try {
    // 0. Create temp directory
    console.log('\n1. Creating temporary directory...');
    await fs.mkdir(tempDir, { recursive: true });

    // 1. Convert PNG to WebP in temp directory
    console.log('\n2. Converting PNG files to WebP...');
    await execAsync(`npx @dhemeira/convert-image resources/js/assets/images --webp --output ${tempDir}`);

    // 2. Move files and cleanup
    console.log('\n3. Moving converted files and cleaning up...');
    const convertedFiles = await glob(`${tempDir}/**/*.webp`);

    const processedFiles = [];
    for (const tempWebpFile of convertedFiles) {
      try {
        // Calculate the final destination path
        const relativePath = path.relative(tempDir, tempWebpFile);
        const finalPath = path.join('resources/js/assets/images', relativePath);
        const originalPngFile = finalPath.replace('.webp', '.png');

        // Create directory if it doesn't exist
        await fs.mkdir(path.dirname(finalPath), { recursive: true });

        // Move WebP file to final location
        await fs.rename(tempWebpFile, finalPath);

        // Delete original PNG if WebP file was moved successfully
        await fs.unlink(originalPngFile);

        console.log(`✓ Processed: ${originalPngFile} → ${finalPath}`);
        processedFiles.push(originalPngFile);
      } catch (error) {
        console.log(`⚠️ Error processing ${tempWebpFile}: ${error.message}`);
      }
    }

    // 3. Clean up temp directory
    await fs.rm(tempDir, { recursive: true, force: true });

    // 4. Update references only if we have processed files
    if (processedFiles.length > 0) {
      console.log('\n4. Updating file references...');
      const filesToSearch = await glob(
        ['resources/**/*.{js,jsx,ts,tsx}', 'resources/**/*.{css,scss}', 'resources/**/*.{php,blade.php}'],
        { ignore: ['**/node_modules/**', '**/vendor/**'] },
      );

      for (const file of filesToSearch) {
        try {
          let content = await fs.readFile(file, 'utf8');
          const originalContent = content;

          // Replace .png extensions with .webp only for files we've successfully converted
          for (const pngFile of processedFiles) {
            const pngBasename = path.basename(pngFile, '.png');
            const regex = new RegExp(`${pngBasename}\\.png(['"\`\\s\\)])`, 'g');
            content = content.replace(regex, `${pngBasename}.webp$1`);
          }

          if (content !== originalContent) {
            await fs.writeFile(file, content, 'utf8');
            console.log(`✓ Updated references in: ${file}`);
          }
        } catch (error) {
          console.error(`❌ Error processing ${file}:`, error.message);
        }
      }

      console.log(`\n✅ Successfully converted ${processedFiles.length} files and updated their references.`);
    } else {
      console.log('\n⚠️ No files were converted. Skipping reference updates.');
    }
  } catch (error) {
    console.error('\n❌ Error:', error.message);
    // Clean up temp directory in case of error
    try {
      await fs.rm(tempDir, { recursive: true, force: true });
    } catch (cleanupError) {
      console.error('Failed to clean up temporary directory:', cleanupError.message);
    }
    process.exit(1);
  }
}

convertToWebp();
