import { useTreeStore } from '@/store/tree';
import { NodeModel } from '@/types/models';
import axios from 'axios';
import { useCallback } from 'react';

export type Action =
  | { type: 'drag-node'; node: NodeModel; payload: { x: number; y: number } }
  | { type: 'recolor-node'; node: NodeModel; payload: string };

export const useHistoryActions = () => {
  const updateNode = useTreeStore((s) => s.updateNode);

  const appendToHistory = useCallback(({ node, payload, type }: Action) => {
    switch (type) {
      case 'drag-node':
        node.style.x = payload.x;
        node.style.y = payload.y;

        updateNode(node.id.toString(), node);

        axios.put(route('branches.nodes.style', node.id), { style: payload });
        break;

      case 'recolor-node':
        node.bg_color = payload;

        updateNode(node.id.toString(), node);

        axios.put(route('branches.nodes.style', node.id), { bg_color: payload });
        break;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return { appendToHistory };
};
