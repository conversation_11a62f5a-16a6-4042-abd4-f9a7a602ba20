import { calculateLayout } from '@/components/tree/calculate-layout';
import { TreeSettings } from '@/Pages/Tenant/Branch/Show';
import { useTreeStore } from '@/store/tree';
import { NodeModel } from '@/types/models';
import { router, usePage } from '@inertiajs/react';
import { useCallback } from 'react';

export const useTreeLayout = () => {
  const settings = usePage().props.settings as TreeSettings;

  const nodes = useTreeStore((s) => s.nodes);
  const setNodes = useTreeStore((s) => s.setNodes);
  const setEdges = useTreeStore((s) => s.setEdges);

  const updateLayout = useCallback(
    (nodeModels: NodeModel[]) => {
      const layout = calculateLayout({
        tree: nodeModels,
        preferNodePosition: settings.layout_mode === 'manual',
      });

      setNodes(layout.nodes);
      setEdges(layout.edges);
    },
    [setEdges, setNodes, settings.layout_mode],
  );

  const applyAutoLayout = useCallback(() => {
    if (!confirm('هل أنت متأكد من تطبيق الترتيب التلقائي؟ ستفقد الترتيب اليدوي  الحالي')) {
      return;
    }

    const { nodes: layoutedNodes } = calculateLayout({
      tree: nodes.map((n) => n.data.nodeModel),
      preferNodePosition: false,
    });

    router.put(
      route('branches.nodes.bulk-style'),
      {
        nodes: layoutedNodes.map((n) => ({
          id: n.data.nodeModel.id,
          style: { x: n.position.x, y: n.position.y },
        })),
      },
      {
        onSuccess: () => setNodes(layoutedNodes),
      },
    );
  }, [nodes, setNodes]);

  return {
    settings,
    updateLayout,
    applyAutoLayout,
  };
};
