import { NodeModel } from '@/types/models';
import { create } from 'zustand';

export const NodeModalType = {
  NONE: 'NONE',
  NODE_INFO: 'NODE_INFO',
} as const;

export type NodeModalTypeValue = (typeof NodeModalType)[keyof typeof NodeModalType];

type NodeModalState = {
  activeModal: NodeModalTypeValue;
  selectedNode: NodeModel | null;
};

type NodeModalActions = {
  setNodeModal: (modalType: NodeModalTypeValue, node?: NodeModel | null) => void;
  hideNodeModal: () => void;
  setSelectedNode: (node: NodeModel | null) => void;
};

type NodeModalStore = NodeModalState & NodeModalActions;

export const useNodeModalStore = create<NodeModalStore>((set) => ({
  activeModal: NodeModalType.NONE,
  selectedNode: null,

  setNodeModal: (modalType, node = null) =>
    set({
      activeModal: modalType,
      selectedNode: node,
    }),

  hideNodeModal: () =>
    set({
      activeModal: NodeModalType.NONE,
      selectedNode: null,
    }),

  setSelectedNode: (node) => set({ selectedNode: node }),
}));
