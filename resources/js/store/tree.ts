import { NodeModel } from '@/types/models';
import { applyEdgeChanges, applyNodeChanges, Edge, Node, OnEdgesChange, OnNodesChange } from '@xyflow/react';
import { create } from 'zustand';

export type AppNode = Node<
  {
    nodeModel: NodeModel;
    direction?: 'TB' | 'LR';
    isRoot?: boolean;
    children?: number[];
  },
  'baseNode'
>;

export type TreeState = {
  // Data
  nodes: AppNode[];
  edges: Edge[];
  nodesSettings: {
    onClickNode?: (node: NodeModel) => void;
    editable?: boolean;
  };
  // Actions
  onNodesChange: OnNodesChange<AppNode>;
  onEdgesChange: OnEdgesChange;
  setNodes: (nodes: AppNode[]) => void;
  setEdges: (edges: Edge[]) => void;
  updateNode: (nodeId: string, newNodeModel: Partial<NodeModel>) => void;
  removeNode: (nodeId: string) => void;
  setNodesSettings: (nodesSettings: { onClickNode?: (node: NodeModel) => void; editable?: boolean }) => void;
};

export const useTreeStore = create<TreeState>((set, get) => ({
  nodes: [],
  edges: [],
  nodesSettings: {
    onClickNode: undefined,
    editable: false,
  },
  onNodesChange: (changes) => {
    set({
      nodes: applyNodeChanges(changes, get().nodes),
    });
  },
  onEdgesChange: (changes) => {
    set({
      edges: applyEdgeChanges(changes, get().edges),
    });
  },
  setNodes: (nodes) => {
    set({ nodes });
  },
  setEdges: (edges) => {
    set({ edges });
  },
  updateNode: (nodeId: string, newNodeModel: Partial<NodeModel>) => {
    set({
      nodes: get().nodes.map((node) => {
        if (node.id === nodeId) {
          node.data.nodeModel = {
            ...node.data.nodeModel,
            ...newNodeModel,
          };

          return structuredClone(node);
        }

        return node;
      }),
    });
  },
  removeNode: (nodeId: string) => {
    const nodesToDelete = get()
      .nodes.filter((node) => node.id === nodeId || node.data.nodeModel.parent_id?.toString() === nodeId)
      .map((node) => node.id);

    set({
      nodes: get().nodes.filter((node) => !nodesToDelete.includes(node.id)),
      edges: get().edges.filter((edge) => !nodesToDelete.includes(edge.source) && !nodesToDelete.includes(edge.target)),
    });
  },
  setNodesSettings: (nodesSettings) => {
    set({ nodesSettings });
  },
}));

export const treeStoreSelector = (state: TreeState) => ({
  nodes: state.nodes,
  edges: state.edges,
  nodesSettings: state.nodesSettings,
  onNodesChange: state.onNodesChange,
  onEdgesChange: state.onEdgesChange,
  setNodes: state.setNodes,
  setEdges: state.setEdges,
  updateNode: state.updateNode,
  removeNode: state.removeNode,
  setNodesSettings: state.setNodesSettings,
});
