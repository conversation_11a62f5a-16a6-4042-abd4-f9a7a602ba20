import { Clients } from '@/Pages/Landing/Partials/Clients';
import FAQs from '@/Pages/Landing/Partials/FAQs';
import Features from '@/Pages/Landing/Partials/Features';
import GuestFooter from '@/Pages/Landing/Partials/GuestFooter';
import Hero from '@/Pages/Landing/Partials/Hero';
import Stats from '@/Pages/Landing/Partials/Stats';

interface BranchName {
  name: string;
  id: number;
  nodeCount: number;
  roundedCount: number;
}

interface WelcomeProps {
  nodesCount: number;
  tenantsCount: number;
  usersCount: number;
  branchesNames: BranchName[];
}

export default function Welcome({ nodesCount, tenantsCount, usersCount, branchesNames }: WelcomeProps) {
  return (
    <div className="relative min-h-screen w-full bg-[url('@/assets/images/welcome-bg.webp')] bg-fixed bg-center bg-no-repeat before:fixed before:inset-0 before:z-0 before:bg-gradient-to-r before:from-green-500/98 before:to-green-400/98 before:content-['']">
      <div className="relative z-10 text-gray-800">
        <Hero />

        <section className="-mt-px bg-gray-50 pt-10">
          <Clients branchesNames={branchesNames} />
        </section>
        <section className="bg-gray-50 py-1 pt-4 pb-6">
          <Stats nodesCount={nodesCount} tenantsCount={tenantsCount} usersCount={usersCount} />
        </section>
        <section className="bg-gray-50 pt-4">
          <Features />
        </section>

        <section className="bg-gray-50 py-12">
          <FAQs />
        </section>

        <hr />
        <section className="bg-gray-50 py-12">
          <GuestFooter />
        </section>
      </div>
    </div>
  );
}
