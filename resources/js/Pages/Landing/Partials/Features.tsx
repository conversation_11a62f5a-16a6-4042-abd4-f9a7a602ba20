import AppScreen1 from '@/assets/images/app-screens/1.webp';
import AppScreen2 from '@/assets/images/app-screens/2.webp';
import AppScreen3 from '@/assets/images/app-screens/3.webp';
import AppScreen4 from '@/assets/images/app-screens/4.webp';
import AppScreen5 from '@/assets/images/app-screens/5.webp';
import DesignedTreeExample from '@/assets/images/designed-tree-example.webp';
import VerticalTreeExample from '@/assets/images/vertical-tree-example.webp';

export default function Features() {
  return (
    <div>
      <div className="p-8">
        <div className="mt-12 flex flex-col items-center text-center">
          <h3 className="mb-3 text-xl leading-none font-medium">أنشئ شجرتك عائلتك</h3>
        </div>
        <div className="mt-4 flex justify-center overflow-hidden">
          <img
            className="max-w-full rounded-3xl border-b-2 p-px shadow-inner sm:max-w-lg"
            src={VerticalTreeExample}
            alt="شجرة عائلة عمودية"
          />
        </div>

        <div className="mt-12 flex flex-col items-center text-center">
          <h3 className="mb-3 text-xl leading-none font-medium">صمم شجرة عائلة متناسقة</h3>
        </div>
        <div className="mt-4 flex justify-center overflow-hidden">
          <img
            className="max-w-full rounded-3xl border-b-2 p-1 shadow-inner sm:max-w-lg"
            src={DesignedTreeExample}
            alt="شجرة عائلة مصممة"
          />
        </div>

        <div className="mt-12 flex flex-col items-center text-center">
          <h3 className="mb-3 text-xl leading-none font-medium">شاركها مع العائلة</h3>
        </div>
        <div className="mt-4 flex flex-wrap justify-center overflow-hidden">
          <img
            className="max-w-xs rounded-3xl border-b-2 p-px shadow-inner sm:max-w-64"
            src={AppScreen1}
            alt="شاشة التطبيق 1"
          />
          <img
            className="max-w-xs rounded-3xl border-b-2 p-px shadow-inner sm:max-w-64"
            src={AppScreen2}
            alt="شاشة التطبيق 2"
          />
          <img
            className="max-w-xs rounded-3xl border-b-2 p-px shadow-inner sm:max-w-64"
            src={AppScreen3}
            alt="شاشة التطبيق 3"
          />
          <img
            className="max-w-xs rounded-3xl border-b-2 p-px shadow-inner sm:max-w-64"
            src={AppScreen4}
            alt="شاشة التطبيق 4"
          />
          <img
            className="max-w-xs rounded-3xl border-b-2 p-px shadow-inner sm:max-w-64"
            src={AppScreen5}
            alt="شاشة التطبيق 5"
          />
        </div>
      </div>
    </div>
  );
}
