// src/components/FamilyTreeMarquee.tsx
import alfoaimLogo from '@/assets/images/alfoaim.webp';
import alhamdanLogo from '@/assets/images/alhamdan.webp';
import alhasson<PERSON>ogo from '@/assets/images/alhasson.webp';
import aljotaliLogo from '@/assets/images/aljotali.webp';
import alkhudiryLogo from '@/assets/images/alkhudiry.webp';
import alrishodyLogo from '@/assets/images/alrishody.webp';
import alsaawiLogo from '@/assets/images/alsaawi.webp';
import alsawweLogo from '@/assets/images/alsawwe.jpeg';
import alsofainLogo from '@/assets/images/alsofain.jpg';
import alzomieLogo from '@/assets/images/alzomie.jpeg';
import clsx from 'clsx';
import React, { useMemo } from 'react';

interface Branch {
  id: string | number;
  name: string;
  roundedCount: number;
}

interface Props {
  branchesNames: Branch[];
}

export const Clients: React.FC<Props> = ({ branchesNames }) => {
  const MIN_ITEMS_PER_SLIDER = 10;

  const ensureMinimumItems = (items: Branch[], minCount = MIN_ITEMS_PER_SLIDER) => {
    if (items.length === 0) return [];
    const result = [...items];
    while (result.length < minCount) {
      result.push(...items.slice(0, Math.min(items.length, minCount - result.length)));
    }
    return result;
  };

  const safeSlice = (arr: Branch[], start: number, end: number) => {
    const s = Math.max(0, Math.min(start, arr.length));
    const e = Math.max(0, Math.min(end, arr.length));
    return arr.slice(s, e);
  };

  const num = branchesNames.length;
  const baseSplit = Math.max(MIN_ITEMS_PER_SLIDER, Math.floor(num / 3));

  const firstGroup = useMemo(() => {
    const size = Math.min(baseSplit, num);
    return ensureMinimumItems(safeSlice(branchesNames, 0, size));
  }, [branchesNames, num, baseSplit]);

  const secondGroup = useMemo(() => {
    const start = Math.min(baseSplit, num);
    const idealEnd = start + Math.max(MIN_ITEMS_PER_SLIDER, Math.floor(num / 3));
    let end = Math.min(idealEnd, num);
    const rem = num - end;
    if (rem > 0 && rem < MIN_ITEMS_PER_SLIDER) {
      end = Math.max(start, num - MIN_ITEMS_PER_SLIDER);
      if (end - start < MIN_ITEMS_PER_SLIDER && num - start >= MIN_ITEMS_PER_SLIDER) {
        end = start + MIN_ITEMS_PER_SLIDER;
      }
    }
    return ensureMinimumItems(safeSlice(branchesNames, start, end));
  }, [branchesNames, num, baseSplit]);

  const thirdGroup = useMemo(() => {
    const start = Math.min(baseSplit, num);
    const idealEnd = start + Math.max(MIN_ITEMS_PER_SLIDER, Math.floor(num / 3));
    let end = Math.min(idealEnd, num);
    const rem = num - end;
    if (rem > 0 && rem < MIN_ITEMS_PER_SLIDER) {
      end = Math.max(start, num - MIN_ITEMS_PER_SLIDER);
      if (end - start < MIN_ITEMS_PER_SLIDER && num - start >= MIN_ITEMS_PER_SLIDER) {
        end = start + MIN_ITEMS_PER_SLIDER;
      }
    }
    return ensureMinimumItems(safeSlice(branchesNames, end, num));
  }, [branchesNames, num, baseSplit]);

  const formatNumber = (n?: number | null) => {
    if (n == null) return '٠';
    const digits = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    const s = String(n).replace(/\d/g, (d) => digits[+d]);
    return s.replace(/\B(?=(?:\d{3})+(?!\d))/g, ',');
  };

  const logos = [
    { src: alrishodyLogo, alt: 'Al Rishody Family Logo' },
    { src: alsaawiLogo, alt: 'Al Saawi Family Logo' },
    { src: aljotaliLogo, alt: 'Al Jotali Family Logo' },
    { src: alkhudiryLogo, alt: 'Al Khudiry Family Logo' },
    { src: alsawweLogo, alt: 'Al Sawwe Family Logo' },
    { src: alzomieLogo, alt: 'Al Zomie Family Logo' },
    { src: alfoaimLogo, alt: 'Al Foaim Family Logo' },
    { src: alhassonLogo, alt: 'Al Hasson Family Logo' },
    { src: alsofainLogo, alt: 'Al Sofain Family Logo' },
    { src: alhamdanLogo, alt: 'Al Hamdan Family Logo' },
  ];

  const renderMarqueeRow = (items: Branch[], animationClass: string, gradientFrom: string, gradientTo: string) => (
    <div className="marquee-container">
      <div className={`marquee-track ${animationClass}`}>
        {[...items, ...items].map((b, i) => (
          <div
            key={`${b.id}-${i}`}
            className={clsx(
              'marquee-item mx-2 flex h-[80px] min-w-[140px] flex-shrink-0 cursor-pointer flex-col items-center justify-center rounded-xl',
              `bg-gradient-to-br ${gradientFrom} ${gradientTo} text-white shadow-lg`,
              'transition-all duration-300 ease-out hover:-translate-y-1.5 hover:shadow-xl hover:brightness-110',
            )}
          >
            <div
              className="mb-1 w-full max-w-[110px] overflow-hidden px-1 text-center text-base font-semibold text-ellipsis whitespace-nowrap"
              title={b.name}
            >
              {b.name}
            </div>
            <div className="inline-flex items-baseline gap-1 rounded-full bg-white/20 px-2.5 py-1 text-xs font-medium opacity-95">
              <span>{formatNumber(b.roundedCount)}</span>
              <span className="text-[0.7rem] opacity-80">فرد</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div className="w-full overflow-hidden bg-gradient-to-b from-gray-50 to-white py-16 sm:py-20 md:py-24">
      <div className="relative mx-auto">
        <div className="mb-12 text-center md:mb-16">
          <h2 className="px-2 text-2xl font-bold tracking-tight whitespace-nowrap text-gray-900 sm:text-4xl">
            أكبر منصة شجرات عائلة بالعالم
          </h2>
        </div>

        <div className="mb-12 px-4 md:mb-16">
          <div className="grid grid-cols-4 place-items-center gap-6 sm:grid-cols-5 md:grid-cols-8 lg:grid-cols-10">
            {logos.map((logo) => (
              <img
                key={logo.alt}
                className="aspect-square w-full max-w-[80px] rounded-xl object-contain opacity-70 transition duration-300 ease-in-out hover:opacity-100 hover:shadow-sm"
                src={logo.src}
                alt={logo.alt}
                loading="lazy"
              />
            ))}
          </div>
        </div>

        <div className="space-y-4">
          {renderMarqueeRow(firstGroup, 'animate-marquee-normal', 'from-emerald-500', 'to-emerald-600')}
          {renderMarqueeRow(secondGroup, 'animate-marquee-fast-reverse', 'from-emerald-600', 'to-emerald-700')}
          {renderMarqueeRow(thirdGroup, 'animate-marquee-slow', 'from-emerald-700', 'to-emerald-800')}
        </div>
      </div>
    </div>
  );
};
