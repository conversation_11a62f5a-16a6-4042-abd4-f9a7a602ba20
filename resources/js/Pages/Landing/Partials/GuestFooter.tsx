import { InstagramIcon } from '@hugeicons-pro/core-stroke-standard';
import { HugeiconsIcon } from '@hugeicons/react';
import { Link } from '@inertiajs/react';

export default function GuestFooter() {
  return (
    <div>
      <div className="px-8">
        <div className="flex flex-1 justify-center gap-2">
          <a href="https://www.instagram.com/awraq.app/">
            <HugeiconsIcon icon={InstagramIcon} size={24} />
          </a>
        </div>
        <div className="mt-4 mb-8 flex flex-col items-center justify-center gap-1 text-sm">
          <div>
            <span>مؤسسة</span>
            <span className="font-bold"> أوراق </span>
            <span>لتقنية المعلومات</span>
          </div>
          <div>
            <span> السجل التجاري </span>
            <span className="font-bold">1010744065</span>
          </div>
        </div>

        {/* Added Links Section */}
        <div className="flex flex-col items-center justify-center gap-1.5 pb-4 text-sm underline underline-offset-4">
          <Link href="/refund-policy" target="_blank">
            سياسة الاسترجاع
          </Link>
          <Link href="/privacy-policy" target="_blank">
            سياسة الخصوصية
          </Link>
        </div>
      </div>
    </div>
  );
}
