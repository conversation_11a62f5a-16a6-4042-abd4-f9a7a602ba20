import PaperTreeMobileAppImage from '@/assets/images/paper-tree-mobile-app.webp';
import SidebarImage from '@/assets/images/sidebar.webp';
import Logo from '@/components/Logo';
import { Button } from '@/components/ui/button';
import useUser from '@/hooks/useUser';

import { LogoutIcon, SidebarRight01Icon, TestTubeIcon } from '@hugeicons-pro/core-stroke-standard';
import { HugeiconsIcon } from '@hugeicons/react';
import { Link } from '@inertiajs/react';

export default function Hero() {
  const user = useUser();

  return (
    <div>
      <div className="mb-24 pt-16">
        <div className="mb-8 flex flex-col items-center gap-4 space-y-10 px-4 md:px-16">
          {/* Left Col */}
          <div className="flex flex-col justify-center text-center md:text-right">
            <Logo width={150} height={150} className="mb-4 self-center text-green-50" />
            <h1 className="text-center text-xl font-medium text-white">أوراق، منصة متكاملة لشجرة عائلتك..</h1>
            <p className="mt-3 mb-4 text-center text-sm text-white">
              صمم شجرة عائلتك، وشاركها مع من تحب من الأهل والأصدقاء.🌳
            </p>
            {/* Main CTA - Registration Button */}
            <div className="mb-2 flex flex-col flex-wrap items-center">
              {!user && (
                <Link href={route('login.create')}>
                  <Button
                    variant="default"
                    size="lg"
                    className="border border-green-500 bg-gradient-to-r from-green-500 to-green-600 px-8 text-lg font-bold tracking-wide hover:from-green-400 hover:to-green-500"
                  >
                    <span>✨ إنشاء شجرة عائلة</span>
                  </Button>
                </Link>
              )}
              <div className="mb-4 text-center">
                <span className="text-xs text-white"> سجل الآن واحصل على رصيد 150 فرد مجانا </span>
              </div>
            </div>
            <ul className="flex justify-center">
              {/* Logged in user buttons */}
              {user ? (
                <li className="flex gap-3">
                  <Link href={route('branches.show')}>
                    <Button variant="secondary">
                      <span className="flex items-center gap-2">
                        <HugeiconsIcon icon={SidebarRight01Icon} size={24} color="currentColor" strokeWidth={1.5} />
                        لوحة التحكم
                      </span>
                    </Button>
                  </Link>
                  <Link href={route('logout')} method="post">
                    <Button variant="destructive" className="bg-red-500/80 hover:bg-red-600">
                      <span className="flex items-center gap-2">
                        <HugeiconsIcon icon={LogoutIcon} size={24} color="currentColor" strokeWidth={1.5} />
                        تسجيل خروج
                      </span>
                    </Button>
                  </Link>
                </li>
              ) : (
                <li className="mt-2 flex flex-col items-center gap-y-4">
                  <Link href={route('login.create')}>
                    <Button variant="secondary" className="min-w-[120px] bg-white/80 hover:bg-white">
                      <span className="flex items-center gap-2"> دخول </span>
                    </Button>
                  </Link>
                  <Link href={route('login.login-demo')} method="post">
                    <Button variant="outline" size="sm" className="border-white/20 bg-green-500 text-white">
                      <span className="flex items-center gap-2">
                        <HugeiconsIcon icon={TestTubeIcon} size={24} color="currentColor" strokeWidth={1.5} />
                        دخول بحساب تجريبي
                      </span>
                    </Button>
                  </Link>
                </li>
              )}
            </ul>
            {!user && (
              <div>
                <hr className="mx-auto my-8 w-64 opacity-60" />
                <div className="text-center text-sm text-white">تواصل معنا</div>
                <section className="mt-4 flex justify-center gap-2">
                  <a
                    href="https://wa.me/966550438831"
                    className="group rounded-xl border border-green-400 bg-green-500 p-3 transition-all"
                  >
                    <svg style={{ width: '24px', height: '24px' }} viewBox="0 0 24 24" className="text-white">
                      <path
                        fill="currentColor"
                        d="M12.04 2C6.58 2 2.13 6.45 2.13 11.91C2.13 13.66 2.59 15.36 3.45 16.86L2.05 22L7.3 20.62C8.75 21.41 10.38 21.83 12.04 21.83C17.5 21.83 21.95 17.38 21.95 11.92C21.95 9.27 20.92 6.78 19.05 4.91C17.18 3.03 14.69 2 12.04 2M12.05 3.67C14.25 3.67 16.31 4.53 17.87 6.09C19.42 7.65 20.28 9.72 20.28 11.92C20.28 16.46 16.58 20.15 12.04 20.15C10.56 20.15 9.11 19.76 7.85 19L7.55 18.83L4.43 19.65L5.26 16.61L5.06 16.29C4.24 15 3.8 13.47 3.8 11.91C3.81 7.37 7.5 3.67 12.05 3.67M8.53 7.33C8.37 7.33 8.1 7.39 7.87 7.64C7.65 7.89 7 8.5 7 9.71C7 10.93 7.89 12.1 8 12.27C8.14 12.44 9.76 14.94 12.25 16C12.84 16.27 13.3 16.42 13.66 16.53C14.25 16.72 14.79 16.69 15.22 16.63C15.7 16.56 16.68 16.03 16.89 15.45C17.1 14.87 17.1 14.38 17.04 14.27C16.97 14.17 16.81 14.11 16.56 14C16.31 13.86 15.09 13.26 14.87 13.18C14.64 13.1 14.5 13.06 14.31 13.3C14.15 13.55 13.67 14.11 13.53 14.27C13.38 14.44 13.24 14.46 13 14.34C12.74 14.21 11.94 13.95 11 13.11C10.26 12.45 9.77 11.64 9.62 11.39C9.5 11.15 9.61 11 9.73 10.89C9.84 10.78 10 10.6 10.1 10.45C10.23 10.31 10.27 10.2 10.35 10.04C10.43 9.87 10.39 9.73 10.33 9.61C10.27 9.5 9.77 8.26 9.56 7.77C9.36 7.29 9.16 7.35 9 7.34C8.86 7.34 8.7 7.33 8.53 7.33Z"
                      />
                    </svg>
                  </a>
                </section>
              </div>
            )}
          </div>
        </div>

        {/* Preview Images with Fade Effect */}
        <div className="relative mt-12 flex flex-col items-center gap-8 md:flex-row md:justify-center">
          {/* Existing App Preview */}
          <div className="relative w-64 overflow-hidden md:w-80">
            <img src={PaperTreeMobileAppImage} alt="تطبيق أوراق" />
          </div>

          {/* Sidebar Preview */}
          <div className="relative h-[800px] w-64 overflow-hidden md:w-80">
            <img
              src={SidebarImage}
              alt="لوحة التحكم"
              className="h-full w-full rounded-lg [mask-image:linear-gradient(to_bottom,black_2.5%,transparent_100%)] object-cover object-top"
            />
          </div>
        </div>
      </div>

      <div className="relative -mt-12 text-gray-50 lg:-mt-24">
        <svg viewBox="0 0 1428 174" version="1.1" xmlns="http://www.w3.org/2000/svg">
          <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
            <g transform="translate(-2.000000, 44.000000)" fill="currentColor" fillRule="nonzero">
              <path
                d="M0,0 C90.7283404,0.927527913 147.912752,27.187927 291.910178,59.9119003 C387.908462,81.7278826 543.605069,89.334785 759,82.7326078 C469.336065,156.254352 216.336065,153.6679 0,74.9732496"
                opacity="0.100000001"
              />
              <path
                d="M100,104.708498 C277.413333,72.2345949 426.147877,52.5246657 546.203633,45.5787101 C666.259389,38.6327546 810.524845,41.7979068 979,55.0741668 C931.069965,56.122511 810.303266,74.8455141 616.699903,111.243176 C423.096539,147.640838 250.863238,145.462612 100,104.708498 Z"
                opacity="0.100000001"
              />
              <path
                id="Path-4"
                d="M1046,51.6521276 C1130.83045,29.328812 1279.08318,17.607883 1439,40.1656806 L1439,120 C1271.17211,77.9435312 1140.17211,55.1609071 1046,51.6521276 Z"
                opacity="0.200000003"
              />
            </g>
            <g transform="translate(-4.000000, 76.000000)" fill="currentColor" fillRule="nonzero">
              <path d="M0.457,34.035 C57.086,53.198 98.208,65.809 123.822,71.865 C181.454,85.495 234.295,90.29 272.033,93.459 C311.355,96.759 396.635,95.801 461.025,91.663 C486.76,90.01 518.727,86.372 556.926,80.752 C595.747,74.596 622.372,70.008 636.799,66.991 C663.913,61.324 712.501,49.503 727.605,46.128 C780.47,34.317 818.839,22.532 856.324,15.904 C922.689,4.169 955.676,2.522 1011.185,0.432 C1060.705,1.477 1097.39,3.129 1121.236,5.387 C1161.703,9.219 1208.621,17.821 1235.4,22.304 C1285.855,30.748 1354.351,47.432 1440.886,72.354 L1441.191,104.352 L1.121,104.031 L0.457,34.035 Z" />
            </g>
          </g>
        </svg>
      </div>
    </div>
  );
}
