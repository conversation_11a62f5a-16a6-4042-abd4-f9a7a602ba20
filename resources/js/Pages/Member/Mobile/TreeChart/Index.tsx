import { NodesRequestConfig, Tree } from '@/components/tree/Tree';
import { NodeModel } from '@/types/models';
import { useQueryState } from 'nuqs';
import { useCallback } from 'react';

const Index = () => {
  const [token] = useQueryState('token');

  const nodesRequestConfig: NodesRequestConfig = {
    url: '/api/v1/mobile/nodes',
    headers: {
      Authorization: `Bearer ${token}`,
    },
  };

  const onClickNode = useCallback(
    (node: NodeModel) => window.ReactNativeWebView?.postMessage(JSON.stringify({ node_id: node.id })),
    [],
  );
  alert(34);

  return (
    <div className="relative h-screen border-y bg-white inset-shadow-xs">
      <Tree showSearch={false} nodesRequestConfig={nodesRequestConfig} onClickNode={onClickNode} />
    </div>
  );
};

export default Index;
