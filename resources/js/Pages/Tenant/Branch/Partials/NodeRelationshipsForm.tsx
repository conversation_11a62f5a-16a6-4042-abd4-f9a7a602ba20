import NodeFilter from '@/components/NodeFilter';
import { Button } from '@/components/ui/button';
import Card from '@/components/ui/Card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { cn } from '@/lib/utils';
import { InertiaNodeForm } from '@/Pages/Tenant/Branch/Partials/UpdateNodeForm';
import { NodeModel, RelationshipStatus } from '@/types/models';
import { InertiaFormProps } from '@inertiajs/react';
import { ChangeEvent } from 'react';

type FormRelationship = InertiaNodeForm['relationships'][number];

export type RelationshipsFormProps = {
  form: InertiaFormProps<InertiaNodeForm>;
};

export default function NodeRelationshipsForm({ form }: RelationshipsFormProps) {
  const updateRelationship = <K extends keyof FormRelationship>(index: number, key: K, value: FormRelationship[K]) => {
    const updated = [...form.data.relationships];
    updated[index][key] = value;
    form.setData('relationships', updated);
  };

  const addRelationship = (): void => {
    const newRelationship: FormRelationship = {
      id: Math.random(),
      node: null,
      name: '',
      family_name: '',
      status: 'marriage' as RelationshipStatus,
      is_outside_family: false,
      wife: null,
      husband: null,
      is_new: true,
      is_deleted: false,
    };

    form.setData('relationships', [...form.data.relationships, newRelationship]);
  };

  const removeRelationship = (index: number): void => {
    const updated = [...form.data.relationships];
    if (updated[index].is_new) {
      updated.splice(index, 1);
    } else {
      updated[index].is_deleted = true;
    }
    form.setData('relationships', updated);
  };

  const handleNameChange = (index: number) => (e: ChangeEvent<HTMLInputElement>) => {
    updateRelationship(index, 'name', e.target.value);
  };

  const handleFamilyNameChange = (index: number) => (e: ChangeEvent<HTMLInputElement>) => {
    updateRelationship(index, 'family_name', e.target.value);
  };

  const handleStatusChange = (index: number) => (value: string) => {
    updateRelationship(index, 'status', value as RelationshipStatus);
  };

  const handleNodeSelect = (index: number) => (node: NodeModel | null) => {
    updateRelationship(index, 'node', node);
  };

  return (
    <Card title="الأزواج">
      <div className="flex flex-col gap-4">
        {form.data.relationships
          .filter((r) => !r.is_deleted)
          .map((relationship, index) => (
            <div key={relationship.id} className="bg-sidebar rounded-xl border border-gray-200 p-2">
              <div className="mb-3 font-bold">الزوج / الزوجة #{index + 1}</div>

              {relationship.name || relationship.family_name ? null : (
                <div
                  className={cn({
                    'max-h-8 overflow-hidden opacity-25': relationship.name || relationship.family_name,
                  })}
                >
                  <div className="text-center text-sm font-semibold">زوجة من العائلة</div>
                  <NodeFilter onSelect={handleNodeSelect(index)} selected={relationship.node as NodeModel} />
                </div>
              )}

              {relationship.node ? null : (
                <div>
                  <div className="mb-4 text-center text-sm font-semibold">أو خارج الشجرة</div>

                  <div className="grid grid-cols-2 gap-2">
                    <Input label="الاسم" value={relationship.name} onChange={handleNameChange(index)} />
                    <Input
                      label="اسم العائلة"
                      value={relationship.family_name}
                      onChange={handleFamilyNameChange(index)}
                    />
                  </div>
                </div>
              )}

              <hr className="my-8" />

              <Label className="mt-2 block">الحالة</Label>
              <RadioGroup
                value={relationship.status}
                onValueChange={handleStatusChange(index)}
                className="mt-2 flex flex-wrap gap-2"
              >
                <RadioGroupItem value="marriage" id={`marriage-${index}`} />
                <Label htmlFor={`marriage-${index}`}>زواج</Label>
                <RadioGroupItem value="divorce" id={`divorce-${index}`} />
                <Label htmlFor={`divorce-${index}`}>طلاق</Label>
                <RadioGroupItem value="widow" id={`widow-${index}`} />
                <Label htmlFor={`widow-${index}`}>متوفى</Label>
              </RadioGroup>

              <div className="mt-2 flex justify-end">
                <Button variant="destructive" size="xs" onClick={() => removeRelationship(index)}>
                  إزالة
                </Button>
              </div>
            </div>
          ))}

        <div className="mt-4 text-center">
          <Button variant="outline" onClick={addRelationship}>
            إضافة زوج / زوجة
          </Button>
        </div>
      </div>
    </Card>
  );
}
