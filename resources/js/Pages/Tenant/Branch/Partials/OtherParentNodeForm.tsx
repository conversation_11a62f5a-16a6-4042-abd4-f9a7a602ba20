import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { InertiaNodeForm } from '@/Pages/Tenant/Branch/Partials/UpdateNodeForm';
import { NodeModel, Relationship } from '@/types/models';
import { otherParentInfo } from '@/utils/helpers';
import { InertiaFormProps } from '@inertiajs/react';
import { useQuery } from '@tanstack/react-query';
import axios from 'axios';
import React from 'react';

type Props = {
  parent: NodeModel;
  form: InertiaFormProps<InertiaNodeForm>;
};

export default function OtherParentNodeForm({ parent, form }: Props) {
  const { data: parentRelationships } = useQuery<Relationship[]>({
    queryKey: ['relationships', parent.id],
    queryFn: async () => axios.get(route('relationships.index', parent.id)).then((res) => res.data.data.relationships),
  });

  console.log(parentRelationships);

  const otherParentOptions = React.useMemo(() => {
    if (!parentRelationships) {
      return [];
    }
    console.log(parentRelationships);
    return [
      { id: '', name: 'غير محدد', isOutsideFamily: false },
      ...parentRelationships.map((r) => ({
        id: r.id.toString(),
        name: otherParentInfo(r),
        isOutsideFamily: r.is_outside_family,
      })),
    ];
  }, [parentRelationships]);

  if (otherParentOptions.length < 2) {
    return null;
  }

  return (
    <div className="mb-8 flex items-start gap-x-2 text-xs">
      <span>{parent.gender === 'male' ? 'الأم:' : 'الأب:'}</span>
      <RadioGroup
        value={form.data.other_parent_relationship_id?.toString()}
        onValueChange={(val) => form.setData('other_parent_relationship_id', val ? parseInt(val) : null)}
        className="flex flex-col gap-1"
      >
        {otherParentOptions.map((option) => (
          <div key={option.id ?? 'undefined'} className="flex items-center gap-x-2">
            <RadioGroupItem id={String(option.id)} value={option.id} />
            <Label htmlFor={String(option.id)} className="text-xs">
              {option.name}
              {option.isOutsideFamily && <span className="text-gray-500"> (خارج العائلة)</span>}
            </Label>
          </div>
        ))}
      </RadioGroup>
    </div>
  );
}
