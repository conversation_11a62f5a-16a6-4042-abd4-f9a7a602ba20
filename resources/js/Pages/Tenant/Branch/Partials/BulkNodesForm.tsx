import { Button } from '@/components/ui/button';
import Card from '@/components/ui/Card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useTreeLayout } from '@/hooks/use-tree-layout';
import { useNodeModalStore } from '@/store/node-modal';
import { useTreeStore } from '@/store/tree';
import { Gender, LifeStatus, NodeModel } from '@/types/models';
import { MinusSignIcon, PlusSignIcon } from '@hugeicons-pro/core-stroke-standard';
import { HugeiconsIcon } from '@hugeicons/react';
import { useForm } from '@inertiajs/react';
import { FormEvent } from 'react';

type NodeFormData = {
  name: string;
  gender: Gender;
  life_status: LifeStatus;
};

type InertiaBulkNodesForm = {
  parent_id: number;
  nodes: NodeFormData[];
};

export default function BulkNodesForm() {
  const { updateLayout } = useTreeLayout();
  const nodes = useTreeStore((s) => s.nodes);

  const nodeId = useNodeModalStore((s) => s.selectedNode!.id);
  const hideNodeModal = useNodeModalStore((s) => s.hideNodeModal);

  const form = useForm<InertiaBulkNodesForm>({
    parent_id: nodeId,
    nodes: [
      {
        name: '',
        gender: 'male',
        life_status: 'unknown',
      },
    ],
  });

  const newNode = () => {
    form.setData('nodes', [...form.data.nodes, { name: '', gender: 'male', life_status: 'unknown' }]);
  };

  const removeNode = (index: number) => {
    form.setData(
      'nodes',
      form.data.nodes.filter((_, i) => i !== index),
    );
  };

  const updateValue = <K extends keyof NodeFormData>(index: number, key: K, value: NodeFormData[K]) => {
    const updatedNodes = [...form.data.nodes];
    updatedNodes[index][key] = value;
    form.setData('nodes', updatedNodes);
  };

  const submit = (e: FormEvent) => {
    e.preventDefault();
    form.post(route('nodes.bulk-store'), {
      onSuccess: ({ props: { data } }) => {
        const newModels = (data as { nodes: NodeModel[] }).nodes;

        updateLayout(nodes.map((n) => n.data.nodeModel).concat(newModels));

        hideNodeModal();
      },
    });
  };

  return (
    <div className="space-y-2">
      {form.data.nodes.map((node, index) => (
        <Card key={index}>
          {/* Name Input */}
          <div>
            <Input
              value={node.name}
              onChange={(e) => updateValue(index, 'name', e.target.value)}
              label={`اسم الفرد #${index + 1}`}
              className="w-full"
            />
          </div>
          {/* Gender and Life Status */}
          <div className="mt-2 grid grid-cols-1 gap-4 sm:grid-cols-2">
            {/* Gender Radio */}
            <div>
              <Label className="text-sm font-medium">الجنس</Label>
              <RadioGroup
                value={node.gender}
                onValueChange={(value) => updateValue(index, 'gender', value as Gender)}
                className="mt-2 flex gap-4"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="male" id={`male-${index}`} />
                  <Label htmlFor={`male-${index}`} className="text-sm">
                    ذكر
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="female" id={`female-${index}`} />
                  <Label htmlFor={`female-${index}`} className="text-sm">
                    أنثى
                  </Label>
                </div>
              </RadioGroup>
            </div>

            {/* Life Status Radio */}
            <div>
              <Label className="text-sm font-medium">حالة الحياة</Label>
              <RadioGroup
                value={node.life_status}
                onValueChange={(value) => updateValue(index, 'life_status', value as LifeStatus)}
                className="mt-2 flex flex-wrap gap-3"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="alive" id={`alive-${index}`} />
                  <Label htmlFor={`alive-${index}`} className="text-sm">
                    حي
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="dead" id={`dead-${index}`} />
                  <Label htmlFor={`dead-${index}`} className="text-sm">
                    متوفى
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="unknown" id={`unknown-${index}`} />
                  <Label htmlFor={`unknown-${index}`} className="text-sm">
                    غير معروف
                  </Label>
                </div>
              </RadioGroup>
            </div>
          </div>
          {/* Delete Button */}
          <div className="flex justify-end">
            <Button
              variant="destructive"
              size="xs"
              disabled={form.data.nodes.length <= 1}
              className="mt-4"
              onClick={() => removeNode(index)}
            >
              <HugeiconsIcon icon={MinusSignIcon} size={8} />
              <span>حذف الفرد</span>
            </Button>
          </div>{' '}
        </Card>
      ))}

      <div className="flex justify-center">
        <Button type="button" variant="outline" onClick={newNode}>
          <HugeiconsIcon icon={PlusSignIcon} size={18} />
          <span>إضافة فرد آخر</span>
        </Button>
      </div>

      <div className="sticky bottom-0 z-50 flex justify-center rounded-lg border border-gray-200/50 bg-gray-100/25 p-4 shadow-xs backdrop-blur-md">
        <Button onClick={submit} disabled={form.processing} className="w-[70%]">
          إضافة الأفراد
        </Button>
      </div>
    </div>
  );
}
