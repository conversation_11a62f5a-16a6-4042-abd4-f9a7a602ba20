import { Button } from '@/components/ui/button';
import Card from '@/components/ui/Card';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import ValidationErrors from '@/components/ValidationErrors';

import { ListSettingIcon } from '@hugeicons-pro/core-stroke-standard';
import { HugeiconsIcon } from '@hugeicons/react';
import { router, useForm } from '@inertiajs/react';
import { useState } from 'react';

type Props = {
  showChangeRoot?: boolean;
  showAddToPaper?: boolean;
  showUpdateFullName?: boolean;
  showResetStyle?: boolean;
  showExportExcel?: boolean;
};

export default function GeneralActions({
  showChangeRoot = false,
  showAddToPaper = false,
  showUpdateFullName = false,
  showResetStyle = false,
  showExportExcel = false,
}: Props) {
  const [isChangeRootOpen, setIsChangeRootOpen] = useState(false);
  const [isResetStyleOpen, setIsResetStyleOpen] = useState(false);

  const form = useForm({
    new_parent_name: '',
  });

  const changeRoot = () => {
    form.post(route('branches.nodes.change-root'), {
      onSuccess: () => location.reload(),
    });
  };

  const addToPaper = () => {
    router.post(route('branches.nodes.add-all-to-paper'), {}, { onSuccess: () => location.reload() });
  };

  const resetStyle = () => {
    router.put(route('branches.nodes.reset-style'), {}, { onSuccess: () => location.reload() });
  };

  return (
    <>
      <div className="flex gap-x-2">
        <DropdownMenu modal={false}>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="flex gap-x-1">
              <HugeiconsIcon icon={ListSettingIcon} size={18} />
              <span>إجراءات عامة</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="me-6 divide-y divide-gray-100">
            {showUpdateFullName && (
              <DropdownMenuItem
                onClick={() => router.post(route('branches.nodes.update-full-name'))}
                className="flex flex-col items-start"
              >
                <span>تحديث الاسم الكامل لجميع الأفراد</span>
                <div className="text-[10px] text-gray-500">مفيد أذا كان هنا خلل في الاسم الكامل لبعض الأفراد</div>
              </DropdownMenuItem>
            )}
            {showResetStyle && (
              <DropdownMenuItem onClick={() => setIsResetStyleOpen(true)} className="flex flex-col items-start">
                <span>إزالة ترتيب جميع الأفراد</span>
                <div className="text-[10px] text-gray-500">
                  مفيد أذا كان هنا خلل وتداخل في ترتيب الأفراد، وتريد البدء من جديد.
                </div>
              </DropdownMenuItem>
            )}
            {showChangeRoot && (
              <DropdownMenuItem onClick={() => setIsChangeRootOpen(true)} className="flex flex-col items-start">
                <span>تغيير أصل العائلة</span>
                <div className="text-[10px] text-gray-500">لإضافة جد أعلى للعائلة</div>
              </DropdownMenuItem>
            )}
            {showExportExcel && (
              <DropdownMenuItem asChild className="flex items-start gap-x-2">
                <a className="flex flex-col" href={route('branches.nodes.download')}>
                  <span>تصدير Excel</span>
                  <div className="text-[10px] text-gray-500">تصدير جميع بيانات الأفراد إلى ملف Excel</div>
                </a>
              </DropdownMenuItem>
            )}
            {showAddToPaper && (
              <DropdownMenuItem onClick={addToPaper} className="flex flex-col items-start space-y-1">
                <div className="text-sm font-medium">إضافة جميع الأفراد للشجرة الورقية</div>
                <div className="text-[10px] leading-tight text-gray-500">
                  مفيد بعد الإنتهاء من الشجرة الورقية،
                  <br />
                  وللتمييز مستقبلاً بين الفرد المضاف وغير المضاف للشجرة الورقية
                </div>
                <div className="text-[10px] text-red-500">هذا الإجراء فقط لمن قام بتصميم شجرة ورقية</div>
              </DropdownMenuItem>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* تغيير أصل العائلة */}
      <Dialog open={isChangeRootOpen} onOpenChange={setIsChangeRootOpen}>
        <DialogContent>
          <form
            onSubmit={(e) => {
              e.preventDefault();
              changeRoot();
            }}
          >
            <Card title="تغيير أصل العائلة" className="w-full md:max-w-xl">
              <ValidationErrors form={form} />
              <div className="mt-2">
                <Input
                  label="اسم أصل العائلة الجديد"
                  value={form.data.new_parent_name}
                  onChange={(e) => form.setData('new_parent_name', e.target.value)}
                />
              </div>
              <Button className="mt-2" disabled={form.processing}>
                تغيير
              </Button>
            </Card>
          </form>
        </DialogContent>
      </Dialog>

      {/* إزالة الترتيب */}
      <Dialog open={isResetStyleOpen} onOpenChange={setIsResetStyleOpen}>
        <DialogContent>
          <div className="mt-4 text-center font-medium">هل أنت متأكد من إزالة ترتيب جميع الأفراد؟</div>
          <div className="mt-2 text-center text-xs">
            مفيد أذا كان هنا خلل وتداخل في ترتيب الأفراد، وتريد البدء من جديد.
          </div>
          <div className="mt-6 mb-10 flex justify-center">
            <Button variant="destructive" onClick={resetStyle}>
              إزالة
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
