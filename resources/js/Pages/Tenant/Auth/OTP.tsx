import AppGuestLayout from '@/Layouts/AppGuestLayout';
import AuthenticationCard from '@/components/AuthenticationCard';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

import { useForm } from '@inertiajs/react';
import React, { useEffect } from 'react';

interface OTPProps {
  identifier: string;
  identifier_type: string;
}

const OTP: React.FC<OTPProps> = ({ identifier, identifier_type }) => {
  const form = useForm({ otp: '' });

  const submit = (e?: React.FormEvent) => {
    e?.preventDefault();

    form.transform((data) => ({
      ...data,
      identifier,
      identifier_type,
    }));

    form.post(route('otp.store'));
  };

  useEffect(() => {
    if (form.data.otp.length === 4) {
      submit();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [form.data.otp.length]);

  return (
    <AppGuestLayout>
      <AuthenticationCard typeName="التحقق">
        <form onSubmit={submit}>
          <Input
            value={form.data.otp}
            onChange={(e) => form.setData('otp', e.target.value)}
            label="رقم التحقق"
            type="number"
            error={form.errors.otp}
          />
          <Button className="mt-4 w-full" type="submit" disabled={form.processing}>
            تحقق
          </Button>
        </form>
      </AuthenticationCard>
    </AppGuestLayout>
  );
};

export default OTP;
