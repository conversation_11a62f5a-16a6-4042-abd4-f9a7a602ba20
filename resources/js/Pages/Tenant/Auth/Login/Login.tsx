import AppGuestLayout from '@/Layouts/AppGuestLayout';
import AuthenticationCard from '@/components/AuthenticationCard';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { toEnglishDigits } from '@/utils/helpers';
import { useForm } from '@inertiajs/react';
import React, { useState } from 'react';

export default function Login() {
  const [showMobileLogin, setShowMobileLogin] = useState(false);

  const form = useForm({ identifier: '' });
  const mobileForm = useForm({ identifier: '' });

  const submitEmail = (e: React.FormEvent) => {
    e.preventDefault();
    const identifier = toEnglishDigits(form.data.identifier);
    form.transform(() => ({ identifier, identifier_type: 'email' }));
    form.post(route('login.store'));
  };

  const submitMobile = (e: React.FormEvent) => {
    e.preventDefault();
    const identifier = toEnglishDigits(mobileForm.data.identifier);
    mobileForm.transform(() => ({ identifier, identifier_type: 'mobile' }));
    form.post(route('login.store'));
  };

  return (
    <AppGuestLayout>
      <AuthenticationCard typeName="تسجيل الدخول">
        {/* Google Login Button */}
        <div className="mb-6">
          <a
            href={route('login.google')}
            className="flex w-full items-center justify-center gap-x-2 rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 focus:outline-none"
          >
            <GoogleIcon />
            تسجيل الدخول باستخدام جوجل
          </a>
        </div>

        {/* Separator */}
        <div className="relative mb-6">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-200/50" />
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="bg-white px-2 text-gray-400">أو</span>
          </div>
        </div>

        {/* Email Login Form */}
        {!showMobileLogin ? (
          <form onSubmit={submitEmail}>
            <Input
              value={form.data.identifier}
              onChange={(e) => form.setData('identifier', e.target.value)}
              label="البريد الإلكتروني"
              type="email"
              error={form.errors.identifier}
              placeholder="أدخل بريدك الإلكتروني"
            />
            <Button
              className="mt-4 w-full"
              type="submit"
              disabled={form.processing || mobileForm.processing || form.data.identifier === ''}
            >
              تسجيل الدخول
            </Button>
          </form>
        ) : (
          <form onSubmit={submitMobile}>
            <Input
              value={mobileForm.data.identifier}
              onChange={(e) => mobileForm.setData('identifier', e.target.value)}
              label="رقم الجوال"
              type="tel"
              error={mobileForm.errors.identifier}
              placeholder="أدخل رقم الجوال"
            />
            <Button
              className="mt-4 w-full"
              type="submit"
              disabled={mobileForm.processing || form.processing || mobileForm.data.identifier === ''}
            >
              تسجيل الدخول
            </Button>
          </form>
        )}

        <hr className="my-8 border-gray-200/50" />

        {/* Toggle Mobile/Login */}
        <div className="text-center" onClick={() => setShowMobileLogin(!showMobileLogin)}>
          <Button variant="ghost" className="text-xs text-gray-500" size="sm">
            {showMobileLogin ? 'تسجيل الدخول باستخدام البريد الإلكتروني' : 'تسجيل الدخول برقم الجوال'}
          </Button>
        </div>
      </AuthenticationCard>
    </AppGuestLayout>
  );
}

const GoogleIcon = () => (
  <svg width={24} height={24} xmlns="http://www.w3.org/2000/svg">
    <path
      fill="#4285F4"
      d="M23.745 12.27c0-.79-.07-1.54-.19-2.27h-11.3v4.51h6.47c-.29 1.48-1.14 2.73-2.4 3.58v3h3.86c2.26-2.09 3.56-5.17 3.56-8.82Z"
    />
    <path
      fill="#34A853"
      d="M12.255 24c3.24 0 5.95-1.08 7.93-2.91l-3.86-3c-1.08.72-2.45 1.16-4.07 1.16-3.13 0-5.78-2.11-6.73-4.96h-3.98v3.09C3.515 21.3 7.565 24 12.255 24Z"
    />
    <path
      fill="#FBBC05"
      d="M5.525 14.29c-.25-.72-.38-1.49-.38-2.29s.14-1.57.38-2.29V6.62h-3.98a11.86 11.86 0 0 0 0 10.76l3.98-3.09Z"
    />
    <path
      fill="#EA4335"
      d="M12.255 4.75c1.77 0 3.35.61 4.6 1.8l3.42-3.42C18.205 1.19 15.495 0 12.255 0c-4.69 0-8.74 2.7-10.71 6.62l3.98 3.09c.95-2.85 3.6-4.96 6.73-4.96Z"
    />
  </svg>
);
