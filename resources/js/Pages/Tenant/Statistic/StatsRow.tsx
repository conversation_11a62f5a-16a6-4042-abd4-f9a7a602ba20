type StatsRowProps = {
  label: string;
  value: number | string;
  nested?: boolean;
};

export function StatsRow({ label, value, nested = false }: StatsRowProps) {
  return (
    <li className={`flex items-center justify-between gap-2 text-sm ${nested ? 'mr-4' : ''}`}>
      <div className="flex items-center gap-2">
        {nested && <div className="h-px w-3 text-gray-500" aria-hidden="true" />}
        <span className="font-light">{label}</span>
      </div>
      <span className="px-2 py-1 text-xs font-bold text-gray-700">{value}</span>
    </li>
  );
}
