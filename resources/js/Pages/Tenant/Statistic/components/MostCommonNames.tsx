import { Button } from '@/components/ui/button';
import React, { useMemo, useState } from 'react';
import { StatsRow } from '../StatsRow';
import { EmptyStats } from './EmptyStats';

interface NameStat {
  name: string;
  count: number;
}

interface Props {
  title: string;
  mostCommonFullNames: NameStat[];
}

export const MostCommonNames: React.FC<Props> = ({ title, mostCommonFullNames }) => {
  const [showAll, setShowAll] = useState(false);

  const namesToDisplay = useMemo(() => {
    if (!mostCommonFullNames) return [];
    return showAll ? mostCommonFullNames : mostCommonFullNames.slice(0, 10);
  }, [mostCommonFullNames, showAll]);

  if (!mostCommonFullNames) {
    return <EmptyStats />;
  }

  return (
    <ul className="flex flex-col space-y-4">
      <li className="flex justify-between border-b-2 border-green-100 font-bold">
        <span className="text-gray-800">
          {title}
          <br />
          <span className="text-xs font-normal text-gray-500">الفلاتر لا تنطبق على الإحصائية</span>
        </span>
      </li>
      {namesToDisplay.length > 0 ? (
        namesToDisplay.map((nameStat, index) => (
          <StatsRow key={`${nameStat.name}-${index}`} label={nameStat.name} value={nameStat.count} />
        ))
      ) : (
        <EmptyStats />
      )}
      {mostCommonFullNames.length > 10 && (
        <li className="mt-2">
          <Button variant="link" onClick={() => setShowAll(!showAll)} className="text-sm">
            {showAll ? 'عرض أقل' : 'عرض المزيد'}
          </Button>
        </li>
      )}
    </ul>
  );
};

export default MostCommonNames;
