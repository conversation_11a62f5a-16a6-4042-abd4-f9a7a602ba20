import AppLayout from '@/Layouts/AppLayout';
import ContactUsMobile from '@/components/ContactUsMobile';
import SubHeader from '@/components/typography/SubHeader';
import Card from '@/components/ui/Card';
import { Box } from '@/components/ui/box';
import { Button } from '@/components/ui/button';
import { Link } from '@inertiajs/react';

export default function Index() {
  return (
    <AppLayout>
      <div>
        <Card title="أهلاً بك في أوراق، منصة شجرة العائلة">
          هذه المنصة تتيح لك إنشاء وإدارة شجرة عائلتك بطريقة سهلة وفعالة. يمكنك إضافة أفراد العائلة، وتنظيمهم، وعرض
          العلاقات بينهم بشكل مرئي جذاب.
        </Card>

        <div className="mt-4 mb-12 grid gap-8 md:grid-cols-2">
          <Card title="إدارة الشجرة">
            <p className="mb-4">
              شجرة العائلة هي تمثيل مرئي لأفراد عائلتك وعلاقاتهم ببعضهم البعض. يمكنك إضافة أفراد جدد، وتعديل معلوماتهم،
              وتنظيم العلاقات بينهم.
            </p>
            <div className="mt-4">
              <Link href={route('branches.show')}>
                <Button size="sm">إدارة الشجرة</Button>
              </Link>
            </div>
          </Card>

          <Card title="الشجرة الورقية">
            <p className="mb-4">
              الشجرة الورقية هي شجرة مصممة من مصممين متخصصين لأجل طباعتها وعرضها في الاجتماعات والمجالس. للحصول على نسخة
              ورقية من الشجرة، يرجى التواصل معنا.
            </p>
            <div className="mt-4">
              <ContactUsMobile />
            </div>
          </Card>
        </div>

        <SubHeader>الخطوات الأساسية للبدء</SubHeader>

        <div className="mb-8 space-y-4">
          <Box>
            <div className="flex items-center gap-3">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-100 text-gray-800">1</div>
              <SubHeader>إضافة أفراد العائلة</SubHeader>
            </div>
            <p className="mt-2 pr-11 text-gray-600">
              ابدأ بإضافة أفراد العائلة الأساسيين. يمكنك إضافة الأب والأم والأبناء والبنات وغيرهم من أفراد العائلة.
            </p>
            <div className="mt-4 pr-11">
              <Link href={route('branches.show')}>
                <Button size="sm">إدارة الشجرة</Button>
              </Link>
            </div>
          </Box>

          <Box>
            <div className="flex items-center gap-3">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-100 text-gray-800">2</div>
              <SubHeader>إعداد مشاركة الشجرة عبر التطبيق</SubHeader>
            </div>
            <p className="mt-2 pr-11 text-gray-600">
              قم بإعداد إعدادات مشاركة الشجرة عبر تطبيق الجوال لتمكين أفراد العائلة من الاطلاع على الشجرة.
            </p>
            <div className="mt-4 flex gap-2 pr-11">
              <Link href={route('settings.app.index')}>
                <Button size="sm">إعدادات التطبيق</Button>
              </Link>
              <Link href={route('settings.general.index')}>
                <Button size="sm">إعدادات العائلة</Button>
              </Link>
            </div>
          </Box>

          <Box>
            <div className="flex items-center gap-3">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-100 text-gray-800">3</div>
              <SubHeader>طلب تصميم شجرة ورقية</SubHeader>
            </div>
            <p className="mt-2 pr-11 text-gray-600">
              إذا كنت ترغب في الحصول على طلب تصميم شجرة ورقية من شجرة العائلة، يمكنك التواصل معنا.
            </p>
            <div className="mt-4 pr-11">
              <ContactUsMobile />
            </div>
          </Box>
        </div>
      </div>
    </AppLayout>
  );
}
