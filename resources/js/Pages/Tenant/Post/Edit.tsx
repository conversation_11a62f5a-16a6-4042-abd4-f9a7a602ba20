import AppLayout from '@/Layouts/AppLayout';
import { Post } from '@/types/models';
import PostForm from './PostForm';

interface Props {
  post: Post;
}

export default function Edit({ post }: Props) {
  return (
    <AppLayout header={<h2 className="text-xl leading-tight font-semibold text-gray-800">تعديل منشور</h2>}>
      <PostForm post={post} submitButtonText="تعديل" onSubmit={(form) => form.post(route('posts.update', post.id))} />
    </AppLayout>
  );
}
