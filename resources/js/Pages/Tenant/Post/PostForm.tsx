import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import ValidationErrors from '@/components/ValidationErrors';
import PostLinks from '@/Pages/Tenant/Post/PostLinks';
import { Post } from '@/types/models';
import { InertiaFormProps, useForm } from '@inertiajs/react';
import dayjs from 'dayjs';
import { ChangeEvent, FormEvent } from 'react';

interface PostFormProps {
  post?: Post;
  submitButtonText?: string;
  onSubmit: (form: InertiaFormProps<PostForm>) => void;
}

export type PostForm = {
  title: Post['title'];
  image: File | null;
  content: Post['content'];
  should_publish: boolean;
  start_at: Post['start_at'];
  end_at: Post['end_at'];
  links: Post['links'];
};

export default function PostForm({ post, submitButtonText = 'حفظ', onSubmit }: PostFormProps) {
  const form = useForm<PostForm>({
    title: post?.title ?? '',
    image: null,
    content: post?.content ?? '',
    should_publish: Boolean(post?.published_at ?? false),
    start_at: post?.start_at ? dayjs(post?.start_at).format('YYYY-MM-DDTHH:mm') : '',
    end_at: post?.end_at ? dayjs(post?.end_at).format('YYYY-MM-DDTHH:mm') : '',
    links: post?.links ?? [],
  });

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    onSubmit(form);
  };

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    form.setData('image', file);
  };

  return (
    <>
      <ValidationErrors />
      <form onSubmit={handleSubmit}>
        {post?.image_url && (
          <div className="mb-6">
            <img src={post.image_url} width={150} className="mx-auto block rounded-lg" alt="Post image" />
          </div>
        )}

        <div className="mt-4">
          <Input
            value={form.data.title}
            onChange={(e) => form.setData('title', e.target.value)}
            label="العنوان"
            error={form.errors.title}
            required
          />
        </div>

        <div className="mt-4">
          <Input label="الصورة" type="file" accept="image/*" onChange={handleFileChange} error={form.errors.image} />
        </div>

        <div className="mt-4">
          <Textarea
            value={form.data.content}
            onChange={(e) => form.setData('content', e.target.value)}
            rows={10}
            label="المحتوى"
            error={form.errors.content}
          />
        </div>

        {/* Event Details */}
        <div className="mt-6 rounded-lg border border-gray-200 bg-gray-50 p-4">
          <h3 className="mb-4 font-medium text-gray-900">تفاصيل المناسبة</h3>
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <Input
                value={form.data.start_at}
                onChange={(e) => form.setData('start_at', e.target.value)}
                type="datetime-local"
                label="تاريخ البداية"
                error={form.errors.start_at}
              />
              <p className="mt-1 text-xs text-gray-500">متى تبدأ المناسبة؟</p>
            </div>
            <div>
              <Input
                value={form.data.end_at}
                onChange={(e) => form.setData('end_at', e.target.value)}
                type="datetime-local"
                label="تاريخ النهاية"
                error={form.errors.end_at}
              />
              <p className="mt-1 text-xs text-gray-500">متى تنتهي المناسبة؟</p>
            </div>
          </div>
        </div>

        {/* Links Section */}
        <div className="mt-6">
          <PostLinks value={form.data.links} onChange={(links) => form.setData('links', links)} />
        </div>

        <div className="mt-4 flex items-center gap-2">
          <Checkbox
            id="should_publish"
            checked={form.data.should_publish}
            onCheckedChange={(checked) => form.setData('should_publish', Boolean(checked))}
          />
          <Label htmlFor="should_publish">نشر المقال في التطبيق</Label>
        </div>

        <div className="mt-4 flex items-center justify-between">
          <Button type="submit" disabled={form.processing}>
            {submitButtonText}
          </Button>
        </div>
      </form>
    </>
  );
}
