import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Post } from '@/types/models';
import { LinkIcon, TagIcon } from '@hugeicons-pro/core-stroke-standard';
import { HugeiconsIcon } from '@hugeicons/react';
import { Plus, Trash2 } from 'lucide-react';
import { useEffect, useState } from 'react';

interface PostLinksProps {
  value: Post['links'];
  onChange: (links: Post['links']) => void;
}

export default function PostLinks({ value, onChange }: PostLinksProps) {
  const [links, setLinks] = useState<Post['links']>(value || []);

  useEffect(() => {
    setLinks(value || []);
  }, [value]);

  const addLink = () => onChange([...(links ?? []), { label: '', value: '' }]);

  const removeLink = (index: number) => {
    const newLinks = links ?? [];
    newLinks.splice(index, 1);
    onChange(newLinks);
  };

  const updateLinkLabel = (index: number, label: string) => {
    const newLinks = links ?? [];
    newLinks[index] = { ...newLinks[index], label };
    onChange(newLinks);
  };

  const updateLinkValue = (index: number, value: string) => {
    const newLinks = links ?? [];
    newLinks[index] = { ...newLinks[index], value };
    onChange(newLinks);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-base font-semibold text-gray-900">روابط مفيدة</h3>
          <p className="text-sm text-gray-500">
            أضف روابط مفيدة متعلقة بالمنشور مثل موقع المناسبة على خرائط قوقل، البث المباشر، ألبوم الصور، أو أي روابط
            إضافية
          </p>
        </div>
        <Button type="button" variant="outline" size="sm" onClick={addLink}>
          <Plus className="mr-2 h-4 w-4" />
          إضافة رابط
        </Button>
      </div>

      <div className="space-y-3">
        {links?.map((link, index) => (
          <div
            key={index}
            className="group flex items-center gap-2 rounded-lg border border-gray-200 bg-white p-3 shadow-xs transition-all hover:border-gray-300"
          >
            <div className="flex-1 space-y-2">
              <Input
                value={link.label}
                placeholder="عنوان الرابط..."
                onChange={(e) => updateLinkLabel(index, e.target.value)}
                suffix={<HugeiconsIcon icon={TagIcon} size={16} className="text-gray-500" />}
              />
              <Input
                value={link.value}
                placeholder="https://..."
                dir="ltr"
                onChange={(e) => updateLinkValue(index, e.target.value)}
                suffix={<HugeiconsIcon icon={LinkIcon} size={16} className="text-gray-500" />}
              />
            </div>
            <div className="opacity-0 transition-opacity group-hover:opacity-100">
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="h-8 w-8 text-red-500 hover:bg-red-50 hover:text-red-600"
                onClick={() => removeLink(index)}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        ))}
      </div>

      {/* Examples Section */}
      {links?.length === 0 && (
        <div className="rounded-lg border border-gray-100 bg-gray-50 p-4">
          <h4 className="mb-2 text-sm font-medium text-gray-700">أمثلة على الروابط:</h4>
          <ul className="list-inside list-disc space-y-1 text-sm text-gray-600">
            <li>رابط موقع المناسبة على خرائط قوقل</li>
            <li>رابط البث المباشر للمناسبة</li>
            <li>رابط ألبوم الصور في قوقل درايف</li>
            <li>رابط التسجيل في المناسبة</li>
            <li>أي روابط إضافية متعلقة بالمنشور</li>
          </ul>
        </div>
      )}
    </div>
  );
}
