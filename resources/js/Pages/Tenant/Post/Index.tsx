import AppLayout from '@/Layouts/AppLayout';
import Pagination, { PaginationData } from '@/components/Pagination';
import Card from '@/components/ui/Card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Link, router } from '@inertiajs/react';
import dayjs from 'dayjs';
import * as hijri from 'hijri-js';

interface Post {
  id: number;
  title: string;
  start_at: string;
  end_at: string;
  published_at: string | null;
}

interface Props {
  posts: PaginationData<Post>;
}

export default function Index({ posts }: Props) {
  // Initialize hijri date converter
  const h = hijri.initialize();

  const formatDateTime = (date: string | null) => {
    if (!date) return '-';
    return dayjs(date).format('YYYY/MM/DD HH:mm');
  };

  const formatHijriDateTime = (date: string | null) => {
    if (!date) return '';
    const hijriDate = h.toHijri(dayjs(date).format('DD/MM/YYYY'), '/');
    return `${hijriDate.plain} ${dayjs(date).format('HH:mm')}`;
  };

  const confirmDelete = (post: Post) => {
    if (confirm('هل أنت متأكد من حذف هذا المنشور؟')) {
      router.delete(route('posts.destroy', post.id));
    }
  };

  return (
    <AppLayout
      header={<h2 className="text-xl leading-tight font-semibold text-gray-800">المنشورات</h2>}
      topEnd={
        <Link href={route('posts.create')}>
          <Button>إضافة منشور</Button>
        </Link>
      }
    >
      <Card>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>العنوان</TableHead>
              <TableHead>تاريخ البداية</TableHead>
              <TableHead>تاريخ النهاية</TableHead>
              <TableHead>الحالة</TableHead>
              <TableHead>تاريخ النشر</TableHead>
              <TableHead />
            </TableRow>
          </TableHeader>
          <TableBody>
            {posts.data.map((post) => (
              <TableRow key={post.id}>
                <TableCell className="font-medium">{post.title}</TableCell>
                <TableCell>
                  <div className="flex flex-col gap-1">
                    <span className="text-sm">{formatDateTime(post.start_at)}</span>
                    <span className="text-xs text-gray-500">{formatHijriDateTime(post.start_at)}</span>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex flex-col gap-1">
                    <span className="text-sm">{formatDateTime(post.end_at)}</span>
                    <span className="text-xs text-gray-500">{formatHijriDateTime(post.end_at)}</span>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant={post.published_at ? 'default' : 'secondary'}>
                    {post.published_at ? 'منشور' : 'مسودة'}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="flex flex-col gap-1">
                    <span className="text-sm">{formatDateTime(post.published_at)}</span>
                    <span className="text-xs text-gray-500">{formatHijriDateTime(post.published_at)}</span>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Link href={route('posts.edit', post.id)}>
                      <Button variant="secondary" size="sm">
                        تعديل
                      </Button>
                    </Link>
                    <Button variant="destructive" size="sm" onClick={() => confirmDelete(post)}>
                      حذف
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        <Pagination data={posts} />
      </Card>
    </AppLayout>
  );
}
