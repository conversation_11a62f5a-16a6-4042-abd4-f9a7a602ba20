import AppLayout from '@/Layouts/AppLayout';
import Pagination, { PaginationData } from '@/components/Pagination';
import Helper from '@/components/typography/Helper';
import { Box } from '@/components/ui/box';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useNodeModalStore } from '@/store/node-modal';
import { NodeModel } from '@/types/models';
import { truncate } from '@/utils/helpers';
import { Link, router } from '@inertiajs/react';
import { useQueryState } from 'nuqs';

interface Member {
  node?: {
    full_name: string;
  };
}

interface NodeAddition {
  id: number;
  parent: NodeModel;
  member?: Member;
  status: string;
  status_ar: string;
  created_at: string;
  node_attributes?: NodeModel;
}

interface NodeAdditionsProps {
  nodeAdditions: PaginationData<NodeAddition>;
}

export default function NodeAdditionIndex({ nodeAdditions }: NodeAdditionsProps) {
  const [status, setStatus] = useQueryState('status');

  const setNodeModal = useNodeModalStore((s) => s.setNodeModal);

  const handleStatusChange = (newStatus: string) => {
    setStatus(newStatus);
    router.visit(route('node-additions.index', newStatus ? { status: newStatus } : {}), { preserveState: true });
  };

  const getClass = (status: string): string => {
    const classes = {
      pending: 'bg-yellow-200 text-yellow-700 py-1 px-2 rounded-xl',
      approved: 'bg-green-200 text-green-700 py-1 px-2 rounded-xl',
      rejected: 'bg-red-200 text-red-700 py-1 px-2 rounded-xl',
    };
    return classes[status as keyof typeof classes] || '';
  };

  return (
    <AppLayout
      header={
        <div>
          طلبات الإضافة للشجرة
          <Helper className="mt-1 font-normal">
            هنا ستظهر طلبات الإضافة للشجرة من قبل أفراد العائلة من خلال التطبيق
          </Helper>
        </div>
      }
    >
      <Box>
        <div className="mb-4 flex justify-between">
          <div>
            <Label>تصفية حسب الحالة</Label>
            <RadioGroup value={status || ''} onValueChange={handleStatusChange} className="mt-2.5 flex">
              <div className="flex items-center gap-x-1.5">
                <RadioGroupItem id="pending" value="pending" />
                <Label htmlFor="pending">تحت الانتظار</Label>
              </div>
              <div className="flex items-center gap-x-1.5">
                <RadioGroupItem id="approved" value="approved" />
                <Label htmlFor="approved">موافق</Label>
              </div>
              <div className="flex items-center gap-x-1.5">
                <RadioGroupItem id="rejected" value="rejected" />
                <Label htmlFor="rejected">مرفوض</Label>
              </div>
            </RadioGroup>
          </div>
          <a href={route('node-additions.export')}>
            <Button size="sm">Excel</Button>
          </a>
        </div>

        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>أب الفرد الجديد</TableHead>
              <TableHead>مقدم الطلب</TableHead>
              <TableHead>الحالة</TableHead>
              <TableHead>تاريخ الطلب</TableHead>
              <TableHead />
            </TableRow>
          </TableHeader>
          <TableBody>
            {nodeAdditions.data.map((addition) => (
              <TableRow key={addition.id}>
                <TableCell className="font-semibold">{addition.parent.name}</TableCell>
                <TableCell>{truncate(addition.member?.node?.full_name || '', 100)}</TableCell>
                <TableCell className="flex items-center">
                  <div className={`text-xs font-medium text-gray-900 ${getClass(addition.status)}`}>
                    {addition.status_ar}
                  </div>
                </TableCell>
                <TableCell>{new Date(addition.created_at).toLocaleDateString()}</TableCell>
                <TableCell className="flex gap-2">
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={() => setNodeModal('NODE_INFO', addition.node_attributes)}
                  >
                    عرض
                  </Button>
                  <Button size="sm" variant="secondary" onClick={() => setNodeModal('NODE_INFO', addition.parent)}>
                    عرض الأب
                  </Button>
                  {addition.status === 'pending' && (
                    <>
                      <Link as="button" href={route('node-additions.approve', addition.id)} method="post">
                        <Button size="sm">موافق</Button>
                      </Link>
                      <Link as="button" href={route('node-additions.reject', addition.id)} method="post">
                        <Button size="sm" variant="destructive">
                          رفض
                        </Button>
                      </Link>
                    </>
                  )}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>

        <Pagination data={nodeAdditions} />
      </Box>
    </AppLayout>
  );
}
