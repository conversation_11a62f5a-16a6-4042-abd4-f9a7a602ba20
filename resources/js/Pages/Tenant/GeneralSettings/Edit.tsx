import AppLayout from '@/Layouts/AppLayout';
import { Box } from '@/components/ui/box';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useForm } from '@inertiajs/react';
import { clsx } from 'clsx';
import { ChangeEvent, useMemo } from 'react';

interface Setting {
  key: string;
  value: string | null;
}

interface Props {
  settings: Setting[];
  nodeAttributes: string[];
}

export default function Edit({ settings }: Props) {
  const getSetting = (key: string) => {
    const setting = settings.find((s) => s.key === key);
    return setting ? setting.value : null;
  };

  const form = useForm({
    family_name: getSetting('family_name') || '',
    about: getSetting('about') || '',
    snapchat_link: getSetting('snapchat_link') || '',
    twitter_link: getSetting('twitter_link') || '',
    instagram_link: getSetting('instagram_link') || '',
    youtube_link: getSetting('youtube_link') || '',
    whatsapp_link: getSetting('whatsapp_link') || '',
    family_logo: null as File | null,
  });

  const familyLogo = useMemo(() => {
    const logo = form.data.family_logo ? form.data.family_logo : getSetting('family_logo');
    return logo instanceof File ? URL.createObjectURL(logo) : logo;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [form.data.family_logo, settings]);

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      form.setData('family_logo', files[0]);
    }
  };

  const update = () => {
    form.post(route('settings.general.update'), {
      preserveScroll: true,
    });
  };

  return (
    <AppLayout
      header="الإعدادات العامة"
      topEnd={
        <Button className={clsx(form.processing && 'opacity-25')} disabled={form.processing} onClick={update}>
          حفظ
        </Button>
      }
      footer={
        <Button className={clsx(form.processing && 'opacity-25')} disabled={form.processing} onClick={update}>
          حفظ
        </Button>
      }
    >
      <Box>
        <div className="mb-4">
          <h3 className="mb-2 text-lg font-medium">الأساسية</h3>
          <Input
            value={form.data.family_name}
            onChange={(e) => form.setData('family_name', e.target.value)}
            label="اسم العائلة"
            className="mb-2"
            error={form.errors.family_name}
          />
          <div className="flex items-center gap-4">
            <Input
              label="شعار العائلة"
              type="file"
              accept="image/*"
              className="mb-2 flex-1"
              error={form.errors.family_logo}
              onChange={handleFileChange}
            />
          </div>
          {familyLogo && (
            <div className="mb-4">
              <img src={familyLogo} className="h-64 rounded border" alt="Family logo" />
            </div>
          )}
        </div>

        <hr className="my-6 border-gray-100" />

        <div className="mb-4">
          <Label>نبذة عن العائلة</Label>
          <Textarea
            value={form.data.about}
            onChange={(e) => form.setData('about', e.target.value)}
            placeholder="نبذة عامة..."
            name="about"
          />
          {form.errors.about && <p className="mt-2 text-sm text-red-500">{form.errors.about}</p>}
        </div>

        <hr className="my-6 border-gray-100" />

        <div className="mb-4">
          <div className="mb-2">حسابات العائلة في مواقع التواصل</div>
          <div className="grid grid-cols-1 gap-2 p-2 sm:grid-cols-3">
            <Input
              value={form.data.whatsapp_link}
              onChange={(e) => form.setData('whatsapp_link', e.target.value)}
              dir="ltr"
              label="الواتساب"
              error={form.errors.whatsapp_link}
            />
            <Input
              value={form.data.snapchat_link}
              onChange={(e) => form.setData('snapchat_link', e.target.value)}
              dir="ltr"
              label="سناب شات"
              error={form.errors.snapchat_link}
            />
            <Input
              value={form.data.twitter_link}
              onChange={(e) => form.setData('twitter_link', e.target.value)}
              dir="ltr"
              label="تويتر"
              error={form.errors.twitter_link}
            />
            <Input
              value={form.data.instagram_link}
              onChange={(e) => form.setData('instagram_link', e.target.value)}
              dir="ltr"
              label="انستجرام"
              error={form.errors.instagram_link}
            />
            <Input
              value={form.data.youtube_link}
              onChange={(e) => form.setData('youtube_link', e.target.value)}
              dir="ltr"
              label="يوتيوب"
              error={form.errors.youtube_link}
            />
          </div>
        </div>
      </Box>
    </AppLayout>
  );
}
