import AppLayout from '@/Layouts/AppLayout';
import ValidationErrors from '@/components/ValidationErrors';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useForm } from '@inertiajs/react';
import React from 'react';

interface User {
  id: number;
  name: string;
  mobile: string;
  email?: string;
  is_owner: boolean;
}

interface Props {
  userProp: User;
}

export const Edit: React.FC<Props> = ({ userProp }) => {
  const form = useForm({
    name: userProp.name,
    mobile: userProp.mobile,
    email: userProp.email,
  });

  const submit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    form.put(route('users.update', userProp.id));
  };

  return (
    <AppLayout>
      <template slot="header">تعديل مستخدم</template>

      <ValidationErrors form={form} />
      <form onSubmit={submit}>
        <div className="mt-4">
          <Input
            value={form.data.name}
            onChange={(e) => form.setData('name', e.target.value as string)}
            label="الاسم"
            error={form.errors.name}
            required
          />
        </div>
        <div className="mt-4">
          <Input
            value={form.data.mobile}
            onChange={(e) => form.setData('mobile', e.target.value as string)}
            label="رقم الجوال"
            type="tel"
            pattern="[0-9]*"
            error={form.errors.mobile}
          />
        </div>
        <div className="mt-4">
          <Input
            value={form.data.email}
            onChange={(e) => form.setData('email', e.target.value as string)}
            label="البريد الإلكتروني"
            error={form.errors.email}
            type="email"
          />
        </div>
        <div className="mt-4 flex items-center justify-between">
          <Button type="submit" className={`mx-4 ${form.processing ? 'opacity-25' : ''}`} disabled={form.processing}>
            تعديل
          </Button>
        </div>
      </form>
    </AppLayout>
  );
};

export default Edit;
