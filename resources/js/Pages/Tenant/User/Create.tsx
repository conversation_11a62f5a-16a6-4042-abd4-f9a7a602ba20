import AppLayout from '@/Layouts/AppLayout';
import ValidationErrors from '@/components/ValidationErrors';
import { Box } from '@/components/ui/box';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useForm } from '@inertiajs/react';
import React from 'react';

export const Create: React.FC = () => {
  const form = useForm({
    name: '',
    mobile: '',
    email: '',
  });

  return (
    <AppLayout
      topEnd={
        <Button
          onClick={() => form.post(route('users.store'))}
          className={`mx-4 ${form.processing ? 'opacity-25' : ''}`}
          disabled={form.processing}
        >
          إنشاء
        </Button>
      }
      header="إنشاء مستخدم"
    >
      <Box>
        <ValidationErrors form={form} />

        <div className="mt-4">
          <Input
            value={form.data.name}
            onChange={(e) => form.setData('name', e.target.value)}
            label="الاسم"
            error={form.errors.name}
            required
          />
        </div>
        <div className="mt-4">
          <Input
            value={form.data.mobile}
            onChange={(e) => form.setData('mobile', e.target.value)}
            label="رقم الجوال"
            type="tel"
            pattern="[0-9]*"
            error={form.errors.mobile}
          />
        </div>
        <div className="mt-4">
          <Input
            value={form.data.email}
            onChange={(e) => form.setData('email', e.target.value)}
            label="البريد الإلكتروني"
            error={form.errors.email}
            type="email"
          />
        </div>
      </Box>
    </AppLayout>
  );
};

export default Create;
