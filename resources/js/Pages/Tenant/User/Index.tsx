import AppLayout from '@/Layouts/AppLayout';
import { Box } from '@/components/ui/box';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Link, router } from '@inertiajs/react';
import React from 'react';

interface User {
  id: number;
  name: string;
  mobile: string;
  email?: string;
  is_owner: boolean;
}

interface Props {
  users: User[];
}

export const Index: React.FC<Props> = ({ users }) => {
  const onDelete = (user: User) => {
    if (confirm(`Are you sure you want to delete ${user.name}?`)) {
      router.delete(route('users.destroy', user.id));
    }
  };

  return (
    <AppLayout
      header="المستخدمون"
      topEnd={
        <Link href={route('users.create')}>
          <Button>مستخدم جديد</Button>
        </Link>
      }
    >
      <Box>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>الاسم</TableHead>
              <TableHead>رقم الجوال</TableHead>
              <TableHead>البريد الإلكتروني</TableHead>
              <TableHead />
            </TableRow>
          </TableHeader>
          <TableBody>
            {users.map((user) => (
              <TableRow key={user.id}>
                <TableCell className="font-semibold">{user.name}</TableCell>
                <TableCell>{user.mobile}</TableCell>
                <TableCell>{user.email}</TableCell>
                <TableCell className="flex gap-2">
                  <Link href={route('users.edit', user.id)}>
                    <Button size="sm">تعديل</Button>
                  </Link>
                  <Button size="sm" disabled={user.is_owner} variant="destructive" onClick={() => onDelete(user)}>
                    حذف
                  </Button>
                </TableCell>
                <TableCell></TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Box>
    </AppLayout>
  );
};

export default Index;
