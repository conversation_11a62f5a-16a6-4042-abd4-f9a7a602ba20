import ContactUsMobile from '@/components/ContactUsMobile';
import MobileAppLinks from '@/components/MobileAppLinks';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  InstagramIcon,
  SnapchatIcon,
  TwitterIcon,
  WhatsappIcon,
  YoutubeIcon,
} from '@hugeicons-pro/core-stroke-standard';
import { HugeiconsIcon } from '@hugeicons/react';
import React from 'react';

interface Socials {
  snapchat?: string;
  twitter?: string;
  instagram?: string;
  youtube?: string;
  whatsapp?: string;
}

interface HomeProps {
  logo?: string;
  about?: string;
  family_name?: string;
  socials?: Socials;
}

const Home: React.FC<HomeProps> = ({ logo, about, family_name, socials = {} }) => {
  return (
    <div className="bg-sidebar min-h-screen">
      {/* Hero Section */}
      <section className="bg-linear-to-r from-green-600 via-green-500 to-green-600">
        <div className="mx-auto max-w-4xl px-4 py-24 text-center">
          <div className="mb-10">
            {/* Family logo */}
            {logo && (
              <img
                src={logo}
                alt="شعار العائلة"
                className="mx-auto h-32 w-32 rounded-full border-4 border-white/90 object-contain shadow-xl"
              />
            )}
          </div>
          <h1 className="mb-6 text-5xl font-extrabold tracking-tight text-white md:text-6xl">{family_name}</h1>
          <p className="mx-auto max-w-xl text-lg leading-relaxed text-white/90">{about}</p>
        </div>
      </section>

      {/* Mobile App Download Section */}
      <section className="bg-white py-20">
        <div className="mx-auto max-w-3xl px-4 text-center">
          <h2 className="mb-6 text-3xl font-bold text-green-600">حمّل تطبيق شجرة العائلة</h2>
          <p className="mb-10 text-lg leading-relaxed text-gray-700">
            للوصول إلى بيانات عائلتك أينما كنت، قم بتنزيل تطبيقنا للحفاظ على الاتصال الدائم مع أفراد العائلة.
          </p>
          <div className="flex justify-center gap-6">
            <MobileAppLinks />
          </div>
        </div>

        {/* Admin Notice using Alert component */}
      </section>

      {/* Social Media Links Section */}
      {socials.snapchat || socials.twitter || socials.instagram || socials.youtube || socials.whatsapp ? (
        <section className="py-16">
          <div className="mx-auto max-w-3xl px-4 text-center">
            <h2 className="mb-8 text-2xl font-bold text-green-600">تابعونا على وسائل التواصل</h2>
            <div className="flex justify-center gap-6">
              {socials.snapchat && (
                <a href={socials.snapchat} target="_blank" rel="noopener noreferrer">
                  <HugeiconsIcon icon={SnapchatIcon} className="text-green-600" width={36} height={36} />
                </a>
              )}
              {socials.twitter && (
                <a href={socials.twitter} target="_blank" rel="noopener noreferrer">
                  <HugeiconsIcon icon={TwitterIcon} className="text-green-600" width={36} height={36} />
                </a>
              )}
              {socials.instagram && (
                <a href={socials.instagram} target="_blank" rel="noopener noreferrer">
                  <HugeiconsIcon icon={InstagramIcon} className="text-green-600" width={36} height={36} />
                </a>
              )}
              {socials.youtube && (
                <a href={socials.youtube} target="_blank" rel="noopener noreferrer">
                  <HugeiconsIcon icon={YoutubeIcon} className="text-green-600" width={36} height={36} />
                </a>
              )}
              {socials.whatsapp && (
                <a href={socials.whatsapp} target="_blank" rel="noopener noreferrer">
                  <HugeiconsIcon icon={WhatsappIcon} className="text-green-600" width={36} height={36} />
                </a>
              )}
            </div>
          </div>
        </section>
      ) : null}

      <div className="mx-auto mt-12 mb-4 max-w-3xl px-4">
        <Alert>
          <AlertTitle>هل أنت مدير العائلة؟</AlertTitle>
          <AlertDescription>
            هذه الصفحة مخصصة لأفراد العائلة. للوصول إلى لوحة التحكم الخاصة بك،
            <a href="https://awraq.app" target="_blank" className="text-primary font-medium hover:underline">
              اضغط هنا لتسجيل الدخول كمدير
            </a>
          </AlertDescription>
        </Alert>
      </div>
      {/* Footer */}
      <footer className="py-4">
        <div className="mx-auto max-w-4xl px-4 text-center">
          <div className="grid gap-6 pb-8 sm:grid-cols-3">
            <div>
              <a href="https://awraq.app" className="text-green-500" target="_blank" rel="noopener noreferrer">
                مؤسسة أوراق لتقنية المعلومات
              </a>
            </div>
            <div>
              <span>السجل التجاري:</span>
              <span className="mx-2 font-bold">1010744065</span>
            </div>
            <div className="text-sm">
              <span>للتواصل:</span>
              <ContactUsMobile className="bg-transparent text-green-500" />
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Home;
