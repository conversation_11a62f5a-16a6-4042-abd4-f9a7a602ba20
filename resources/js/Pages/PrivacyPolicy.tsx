import AppGuestLayout from '@/Layouts/AppGuestLayout';
import Logo from '@/components/Logo';

export default function PrivacyPolicy() {
  return (
    <AppGuestLayout>
      <div className="flex min-h-screen flex-col text-sm">
        {/* Header */}
        <header className="mt-12 flex justify-center">
          <Logo width={100} className="text-green-600" />
        </header>

        {/* Main Content */}
        <main className="container mx-auto grow px-4">
          <div className="mt-8 rounded-lg border bg-white p-6">
            <h1 className="mb-6 text-xl font-bold">سياسة الخصوصية</h1>
            <div className="text-wrap whitespace-pre-line text-gray-700">
              {`جمع المعلومات و استخدامها تقع على عاتقنا مهمة حماية معلوماتك الشخصية، ويتم توضيح هذه
السياسة وكيفية استخدام معلوماتك الشخصية عبر موقع أوراق، والتطبيق المتوافر على الأجهزة
الذكية أيفون، أندرويد، ويندوز (المشار إليها جميعا في الموقع). علماً بأن سياسة الخصوصية هذه
لا تنطبق على مواقع شركائنا في العمل، الشركات التابعة، أو أي أطراف أخرى، وحتى إن تم الإشارة
إليهم على الموقع. لذا ننصحك بمراجعة سياسة الخصوصية العائدة للأطراف الأخرى التي تود التعامل
معها. عند استخدامك للخدمات المتوفرة على الموقع أو تطبيقات الجوال، سيتم طلب تقديم بعض
المعلومات مثل اسمك، عناوين الاتصال، بطاقة الائتمان أو بطاقة الخصم. ويتم تخزين هذه
المعلومات والاحتفاظ بها على أجهزة الكمبيوتر أو غيرها. ما هي المعلومات التي سنقوم بجمعها؟ -
معلومات شخصية - معلومات الدفع - معلومات الحجز - معلومات أخرى

طرق استخدام المعلومات
- الوفاء باتفاقنا معك بما في ذلك الحجوزات وأي حجز آخر، تجهيز مناسباتك، أو التواصل معك في حال
وجود أي مشكلة تتعلق بالحجز الخاص بك.
- تسجيل اسمك على موقعنا أو تطبيقات الجوال وبالتالي يمكنك إدارة حسابك على موقعنا لتلقي جميع
خدماتنا. كما يمكنك إلغاء الاشتراك عبر التواصل معنا في حال لم تعد ترغب بالتمتع بهذه الخدمات.
- للإجابة على أي استفسار قمت بإرساله إلينا عبر البريد الالكتروني.
- لأغراض التسويق المباشر، على النحو المبين بالتفصيل أدناه.

من الضروري لدينا معرفة جميع اسماء العملاء الذين حجزوا إذا كنت قد قمت بالحجز بالنيابة عن
شخص آخر، فمن المفترض أن تكون قد حصلت على موافقته لاستخدام معلوماته الشخصية. وسوف
نكمل الإجراءات بناءً على الموافقة المذكورة أعلاه.

- لإجراء الحجوزات، ومعاملات الدفع في الخدمات المتوفرة على الموقع.
- لمنحك تجربة خاصة وتقديم أفضل الخدمات والعمل على تلبية احتياجاتك الفردية على أفضل وجه عبر
تزويدنا بمعلوماتك الشخصية.
- لتطوير أداء استخدام الموقع: نسعى دوماً لتحسين خدمات الموقع استناداً على المعلومات التي

`}
            </div>
          </div>
        </main>

        {/* Footer */}
        <footer className="py-4 text-center text-sm text-gray-500">
          &copy; {new Date().getFullYear()} شجرة العائلة. جميع الحقوق محفوظة.
        </footer>
      </div>
    </AppGuestLayout>
  );
}
