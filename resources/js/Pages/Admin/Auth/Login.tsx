import AuthenticationCard from '@/components/AuthenticationCard';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import ValidationErrors from '@/components/ValidationErrors';

import { useForm } from '@inertiajs/react';
import { FormEventHandler } from 'react';

type AdminLoginProps = {
  status?: string;
};

export default function AdminLogin({ status }: AdminLoginProps) {
  const form = useForm({
    email: '',
    password: '',
  });

  const submit: FormEventHandler = (e) => {
    e.preventDefault();
    form.post(route('admin.login'));
  };

  return (
    <AuthenticationCard typeName="دخول مدير الموقع">
      <ValidationErrors form={form} />

      {status && <div className="mb-4 text-sm font-medium text-green-600">{status}</div>}

      <form onSubmit={submit}>
        <div>
          <Input
            id="email"
            value={form.data.email}
            onChange={(e) => form.setData('email', e.target.value)}
            label="البريد الإلكتروني"
            type="email"
            className="mt-1 block w-full"
          />
        </div>

        <div className="mt-4">
          <Input
            id="password"
            value={form.data.password}
            onChange={(e) => form.setData('password', e.target.value)}
            label="كلمة المرور"
            type="password"
            className="mt-1 block w-full"
          />
        </div>

        <div className="mt-4 flex items-center justify-around">
          <Button className="mx-4" disabled={form.processing}>
            تسجيل دخول
          </Button>
        </div>
      </form>
    </AuthenticationCard>
  );
}
