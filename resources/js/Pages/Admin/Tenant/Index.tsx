import AppAdminLayout from '@/Layouts/AppAdminLayout';
import Pagination, { PaginationData } from '@/components/Pagination';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { debounceReload, truncate } from '@/utils/helpers';
import { router, useForm } from '@inertiajs/react';
import { useQueryState } from 'nuqs';
import React, { useEffect } from 'react';

type Tenant = {
  id: number;
  owner: {
    name: string;
    mobile: string;
  };
  branches: { name: string }[];
  all_nodes_count: number;
  max_nodes_number: number;
  created_at: string;
};

type Props = {
  tenants: PaginationData<Tenant>;
};

export function MaxNodesInput({ tenantId, value }: { tenantId: number; value: number }) {
  const { data, setData, put, processing } = useForm({
    max_nodes_number: value,
  });

  useEffect(() => {
    setData('max_nodes_number', value);
  }, [setData, value]);

  const handleBlur = () => {
    put(route('admin.tenants.update', tenantId), {
      preserveScroll: true,
      preserveState: true,
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleBlur();
  };

  return (
    <form onSubmit={handleSubmit}>
      <input
        type="number"
        value={data.max_nodes_number}
        min={1}
        onChange={(e) => setData('max_nodes_number', parseInt(e.target.value) || 1)}
        onBlur={handleBlur}
        disabled={processing}
        className="w-16 rounded border border-gray-300 px-2 py-1 text-sm"
      />
    </form>
  );
}

export default function AdminTenantsPage({ tenants }: Props) {
  const [text, setText] = useQueryState('text');

  useEffect(() => {
    if (text) {
      debounceReload({ text });
    }
  }, [text]);

  const login = (tenant: Tenant) => {
    router.post(route('admin.tenants.login', tenant.owner));
  };

  return (
    <AppAdminLayout header="العملاء">
      <div className="flex flex-col">
        <Label>بحث</Label>
        <Input value={text ?? ''} onChange={(e) => setText(e.target.value)} className="mb-8" />

        <div className="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
          <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
            <div className="overflow-hidden border-b border-gray-200 shadow-sm sm:rounded-lg">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    {[
                      '#',
                      'اسم المستخدم',
                      'رقم الجوال',
                      'اسم العائلة',
                      'عدد الأفراد',
                      'العدد المسموح',
                      'تاريخ الإنشاء',
                      '',
                    ].map((heading, i) => (
                      <th
                        key={i}
                        className="px-6 py-3 text-right text-xs font-medium tracking-wider text-gray-500 uppercase"
                      >
                        {heading}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white">
                  {tenants.data.map((tenant) => (
                    <tr key={tenant.id}>
                      <td className="px-6 py-4 text-sm font-medium whitespace-nowrap text-gray-900">{tenant.id}</td>
                      <td className="px-6 py-4 text-sm font-medium whitespace-nowrap text-gray-900">
                        {truncate(tenant.owner.name, 10)}
                      </td>
                      <td className="px-6 py-4 text-sm font-medium whitespace-nowrap text-gray-900">
                        {tenant.branches[0]?.name}
                      </td>
                      <td className="px-6 py-4 text-sm font-medium whitespace-nowrap text-gray-900">
                        {tenant.owner.mobile}
                      </td>
                      <td className="px-6 py-4 text-sm font-medium whitespace-nowrap text-gray-900">
                        {tenant.all_nodes_count}
                      </td>
                      <td className="px-6 py-4 text-sm font-medium whitespace-nowrap text-gray-900">
                        <div className="flex items-center gap-2">
                          <div
                            className={`h-4 w-4 rounded-full ${
                              tenant.max_nodes_number !== 150 ? 'bg-green-500' : 'bg-red-500'
                            }`}
                          />
                          <MaxNodesInput tenantId={tenant.id} value={tenant.max_nodes_number} />
                        </div>
                      </td>
                      <td className="px-6 py-4 text-sm font-medium whitespace-nowrap text-gray-900">
                        {tenant.created_at}
                      </td>
                      <td className="flex gap-2 px-6 py-1 text-sm font-medium whitespace-nowrap">
                        <Button size="sm" variant="secondary" onClick={() => login(tenant)}>
                          دخول
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <Pagination data={tenants} />
      </div>
    </AppAdminLayout>
  );
}
