import AdminNavbar from '@/Layouts/Partial/AdminNavbar';
import { ReactNode } from 'react';

type AppAdminLayoutProps = {
  header?: ReactNode;
  children: ReactNode;
};

export default function AppAdminLayout({ header, children }: AppAdminLayoutProps) {
  return (
    <div className="min-h-screen bg-gray-100 text-gray-800">
      <AdminNavbar />

      {/* Page Heading */}
      <header className="bg-white shadow-sm">
        <div className="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
          <h2 className="text-xl leading-tight font-semibold">{header}</h2>
        </div>
      </header>

      {/* Page Content */}
      <main>
        <div className="py-10">
          <div className="mx-auto max-w-7xl sm:px-6 lg:px-8">
            <div className="overflow-hidden bg-white p-4 shadow-xl sm:rounded-lg">{children}</div>
          </div>
        </div>
      </main>
    </div>
  );
}
