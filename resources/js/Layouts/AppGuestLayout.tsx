import GuestFooter from '@/Pages/Landing/Partials/GuestFooter';
import React from 'react';

interface AppGuestLayoutProps {
  children: React.ReactNode;
}

const AppGuestLayout: React.FC<AppGuestLayoutProps> = ({ children }) => {
  return (
    <div className="min-h-screen bg-gray-50">
      <div>{children}</div>
      <hr className="mx-auto my-20 w-[90%] border-gray-200/50" />
      <GuestFooter />
    </div>
  );
};

export default AppGuestLayout;
