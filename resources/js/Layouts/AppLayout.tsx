import AppModal from '@/components/AppModal';
import { AppSidebar } from '@/components/AppSidebar';
import MaxNodesDiscountModal from '@/components/MaxNodesDiscountModal';
import Pricing from '@/components/Pricing';
import { Button } from '@/components/ui/button';
import { SidebarProvider, SidebarTrigger, useSidebar } from '@/components/ui/sidebar';
import { useTenant, useUser } from '@/hooks/useUser';
import { NodeCardModal } from '@/Shared/NodeCard';
import { usePaymentModalStore } from '@/store/payment-modal';
import { Link, usePage } from '@inertiajs/react';
import { ReactFlowProvider } from '@xyflow/react';
import posthog from 'posthog-js';
import { PropsWithChildren, ReactNode, useEffect, useState } from 'react';
import { toast } from 'sonner';

type AppLayoutProps = {
  unstyled?: boolean;
  header?: ReactNode;
  content?: ReactNode;
  topEnd?: ReactNode;
  footer?: ReactNode;
  withoutPadding?: boolean;
};

export default function AppLayout({
  unstyled = false,
  header,
  content,
  children,
  topEnd,
  footer,
  withoutPadding = false,
}: PropsWithChildren<AppLayoutProps>) {
  return (
    <SidebarProvider>
      <AppLayoutContent
        unstyled={unstyled}
        header={header}
        content={content}
        topEnd={topEnd}
        footer={footer}
        withoutPadding={withoutPadding}
      >
        {children}
      </AppLayoutContent>
    </SidebarProvider>
  );
}

const AppLayoutContent = ({
  unstyled = false,
  header,
  content,
  children,
  topEnd,
  footer,
  withoutPadding,
}: PropsWithChildren<AppLayoutProps>) => {
  const user = useUser()!;
  const page = usePage();
  const sidebar = useSidebar();

  // Show toast on first error
  useEffect(() => {
    const firstError = Object.values(page.props.errors ?? {})[0];
    if (firstError) {
      toast.error(firstError as string);
    }
  }, [page.props.errors]);

  // Identify user in PostHog
  useEffect(() => {
    if (user?.id) {
      posthog.identify(user.id.toString(), {
        name: user.name,
        email: user.email,
        mobile: user.mobile,
      });
    }
    // eslint-disable-next-line
  }, [user?.id]);

  return (
    <ReactFlowProvider>
      <AppSidebar />
      {sidebar.isMobile && (
        <Button
          variant="outline"
          size="icon"
          className="fixed top-1.5 left-2.5 z-50 size-13 rounded-full border-gray-300 bg-white shadow"
          onClick={(e) => {
            sidebar.toggleSidebar();
            e.stopPropagation();
          }}
        >
          <div className="flex flex-col items-center gap-y-0.5">
            <SidebarTrigger className="pointer-events-none" size={15} />
            <span className="text-[7px] font-bold text-gray-600">فتح القائمة</span>
          </div>
        </Button>
      )}
      <main className="bg-sidebar w-full text-gray-800">
        {!unstyled && (
          <div className="flex flex-col">
            <NodesLimitPromoBanner />
            <MaxNodesBanner />
            <DemoBanner />
          </div>
        )}
        <NodeCardModal />
        {header || !sidebar.isMobile ? null : null}
        <div className={!withoutPadding ? 'px-6 py-4' : ''}>
          <div className="flex items-center">
            {!unstyled && (
              <div className="flex w-full flex-wrap items-center justify-between sm:flex-nowrap">
                {header ? (
                  <div className="flex items-center gap-5">
                    <header>
                      <span className="leading-tight font-medium">{header}</span>
                    </header>
                  </div>
                ) : (
                  <div></div>
                )}
                <div>{topEnd}</div>
              </div>
            )}
          </div>

          {unstyled ? (
            <div className="relative">{content ?? children}</div>
          ) : (
            (content ?? children) && (
              <main className={`${header ? 'mt-4' : ''}`}>
                {content ?? children}
                <div className="mt-4">{footer}</div>
              </main>
            )
          )}
        </div>
      </main>
    </ReactFlowProvider>
  );
};

const NodesLimitPromoBanner = () => {
  const tenant = useTenant()!;
  const user = useUser()!;
  const [showPricingModal, setShowPricingModal] = useState(false);

  if (user.is_demo) {
    return null;
  }

  const currentNodes = tenant.all_nodes_count || 0;
  const maxNodes = tenant.max_nodes_number;
  const progressPercentage = Math.min((currentNodes / maxNodes) * 100, 100);
  const isNearLimit = progressPercentage > 80;
  const hasReachedLimit = tenant.did_reach_max_number_of_nodes;

  // Show prominent banner for discount-eligible users, subtle for paying customers
  const isDiscountEligible = tenant.is_eligible_for_discount;
  const showSubtleBanner = !isDiscountEligible && !hasReachedLimit;

  // Subtle banner for paying customers
  if (showSubtleBanner) {
    return (
      <>
        <div className="border-b border-gray-200 bg-gray-50">
          <div className="flex items-center justify-end p-3 md:container md:mx-auto">
            <div className="text-right text-xs text-gray-600">
              <span className="inline-block">
                الحد الأقصى للأفراد:
                <span className="mx-1 font-semibold text-green-600">{maxNodes}</span>
                فرد.
              </span>
              <br />
              <span className="mt-1 inline-block">
                <span className="me-2">لشراء كميات إضافية</span>
                <Button
                  size="sm"
                  className="h-6 border-gray-300 px-2 py-1 text-xs text-gray-700 hover:bg-gray-100"
                  onClick={() => setShowPricingModal(true)}
                >
                  اضغط هنا
                </Button>
              </span>
            </div>
          </div>
        </div>

        <AppModal open={showPricingModal} onOpenChange={setShowPricingModal}>
          <div className="p-4">
            <h3 className="mb-6 text-lg font-bold">باقات شجرة العائلة</h3>
            <Pricing />
          </div>
        </AppModal>
      </>
    );
  }

  // Prominent banner for discount-eligible users or those who reached limit
  return (
    <>
      <div
        className={`relative overflow-hidden border-b shadow-sm transition-all duration-300 ${
          hasReachedLimit
            ? 'bg-gradient-to-r from-red-500 to-red-600 text-white'
            : isNearLimit
              ? 'bg-gradient-to-r from-orange-500 to-amber-500 text-white'
              : 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white'
        }`}
      >
        {/* Animated background pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 animate-pulse bg-gradient-to-r from-transparent via-white to-transparent"></div>
        </div>

        <div className="relative flex items-center justify-between p-4 md:container md:mx-auto">
          <div className="flex-1">
            <div className="flex items-center gap-3">
              <div className={`rounded-full p-2 ${hasReachedLimit ? 'bg-white/20' : 'bg-white/20'}`}>
                <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                </svg>
              </div>
              <div>
                <div className="mb-1 text-sm font-medium">
                  {hasReachedLimit ? (
                    <span className="flex items-center gap-2">
                      ⚠️ وصلت للحد الأقصى!
                      <span className="animate-pulse">🔥</span>
                    </span>
                  ) : (
                    'استخدام الأفراد'
                  )}
                </div>
                <div className="text-lg font-bold">
                  <span className={hasReachedLimit ? 'text-yellow-300' : 'text-white'}>{currentNodes}</span>
                  <span className="mx-1">/</span>
                  <span className="text-green-300">{maxNodes}</span>
                  <span className="mr-1 text-sm font-normal">فرد</span>
                </div>

                {/* Progress bar */}
                <div className="mt-2 h-2 w-32 rounded-full bg-white/20">
                  <div
                    className={`h-full rounded-full transition-all duration-500 ${
                      hasReachedLimit ? 'bg-yellow-300' : isNearLimit ? 'bg-white' : 'bg-green-300'
                    }`}
                    style={{ width: `${progressPercentage}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </div>

          <div className="flex-shrink-0">
            <Button
              onClick={() => setShowPricingModal(true)}
              className={`relative overflow-hidden font-bold shadow-lg transition-all duration-300 hover:scale-105 ${
                hasReachedLimit
                  ? 'animate-pulse bg-yellow-400 text-red-800 hover:bg-yellow-500'
                  : 'bg-white text-blue-600 hover:bg-gray-100'
              }`}
              size="sm"
            >
              {/* Shimmer effect */}
              <div className="absolute inset-0 -top-10 skew-x-12 animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/30 to-transparent"></div>

              <span className="relative flex items-center gap-2">
                {hasReachedLimit ? <>🚀 ترقية فورية</> : <>💎 ترقية الآن</>}
              </span>
            </Button>
          </div>
        </div>

        {hasReachedLimit && (
          <div className="bg-black/20 px-4 py-2 text-center text-sm">
            <span className="mr-1 inline-block animate-bounce">⚡</span>
            لا يمكنك إضافة أفراد جدد! قم بالترقية الآن لمتابعة بناء شجرة عائلتك
            <span className="ml-1 inline-block animate-bounce">⚡</span>
          </div>
        )}
      </div>

      <AppModal open={showPricingModal} onOpenChange={setShowPricingModal}>
        <div className="mx-auto max-w-4xl p-6">
          <div className="mb-6 text-center">
            <h3 className="mb-2 text-2xl font-bold text-gray-900">🌟 باقات شجرة العائلة المميزة</h3>
            <p className="text-gray-600">اختر الباقة المناسبة لك وابدأ في بناء شجرة عائلة أكبر وأكثر تفصيلاً</p>
          </div>
          <Pricing />
        </div>
      </AppModal>

      <style jsx global>{`
        @keyframes shimmer {
          0% {
            transform: translateX(-100%) skewX(-12deg);
          }
          100% {
            transform: translateX(200%) skewX(-12deg);
          }
        }
      `}</style>
    </>
  );
};

const MaxNodesBanner = () => {
  const tenant = useTenant()!;
  const user = useUser()!;

  const [maxDiscountModalShown, setMaxDiscountModalShown] = useState(false);
  const openPricingModal = usePaymentModalStore((state) => state.openPricingModal);

  if (user.is_demo) {
    return null;
  }

  if (!tenant.did_reach_max_number_of_nodes) {
    return null;
  }

  return (
    <div className={`border-b p-3 text-white ${tenant.is_eligible_for_discount ? 'bg-green-600' : 'bg-red-500'}`}>
      <div className="flex items-center justify-between md:container md:mx-auto">
        <div>
          <p className="mb-1 text-sm font-medium">
            لقد وصلتم للحد الأقصى من الأفراد <strong>({tenant.max_nodes_number} فرد)</strong>.
          </p>
          <div className="text-sm">
            {tenant.is_eligible_for_discount ? (
              <>
                <strong>عرض خاص:</strong> احصل على 50 فرد إضافي بخصم 15%!{' '}
                <button className="font-bold underline" onClick={() => setMaxDiscountModalShown(true)}>
                  اضغط هنا
                </button>
              </>
            ) : (
              <>
                لشراء كميات إضافية من الأفراد{' '}
                <button className="underline" onClick={() => openPricingModal()}>
                  اضغط هنا
                </button>
              </>
            )}
          </div>
        </div>
      </div>
      <AppModal open={maxDiscountModalShown}>
        <MaxNodesDiscountModal />
      </AppModal>
    </div>
  );
};

const DemoBanner = () => {
  const user = useUser()!;

  if (!user.is_demo) {
    return null;
  }

  return (
    <div className="border-b bg-green-500 p-3 text-white">
      <div className="flex items-center justify-between md:container md:mx-auto">
        <div>
          <p className="mb-1 text-sm font-medium">أنت الأن داخل لحساب النسخة التجريبية</p>
          <div className="text-xs">
            <span>أعجبتك المنصة؟ سجل حسابك من هذا الرابط </span>
            <span>
              <Link href={route('logout')} method="post" as="button" className="ml-1 underline">
                awraq.app
              </Link>
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};
