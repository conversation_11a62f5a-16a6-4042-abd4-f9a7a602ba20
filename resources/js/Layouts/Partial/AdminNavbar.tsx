import Logo from '@/components/Logo';
import NavLink from '@/components/NavLink';
import ResponsiveNavLink from '@/components/ResponsiveNavLink';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useAdmin } from '@/hooks/useUser';
import { Link, useForm } from '@inertiajs/react';
import { useState } from 'react';

export default function AdminNavbar() {
  const [showingNavigationDropdown, setShowingNavigationDropdown] = useState(false);
  const admin = useAdmin()!;
  const { post } = useForm();

  return (
    <nav className="border-b border-gray-100 bg-white">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 justify-between">
          <div className="flex">
            <div className="flex shrink-0 items-center">
              <Link href={route('admin.tenants.index')}>
                <Logo width={48} height={48} className="text-green-600" />
              </Link>
            </div>

            <div className="hidden sm:-my-px sm:flex sm:space-x-5 sm:space-x-reverse">
              <NavLink href={route('admin.tenants.index')} active={route().current('admin.tenants.*')}>
                المستخدمون
              </NavLink>
            </div>
          </div>

          <div className="hidden sm:mx-6 sm:flex sm:items-center">
            <div className="relative mx-3">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <button
                    type="button"
                    className="inline-flex items-center rounded-md border border-transparent bg-white px-3 py-2 text-sm font-medium text-gray-500 transition hover:text-gray-700 focus:outline-none"
                  >
                    {admin.name}
                    <svg
                      className="-mx-0.5 mx-2 h-4 w-4"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </button>
                </DropdownMenuTrigger>

                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>إدارة الحساب</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <form
                    onSubmit={(e) => {
                      e.preventDefault();
                      post(route('logout'));
                    }}
                  >
                    <DropdownMenuItem asChild>
                      <button type="submit" className="w-full text-right">
                        تسجيل خروج
                      </button>
                    </DropdownMenuItem>
                  </form>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          <div className="-mx-2 flex items-center sm:hidden">
            <button
              onClick={() => setShowingNavigationDropdown(!showingNavigationDropdown)}
              className="inline-flex items-center justify-center rounded-md p-2 text-gray-400 transition hover:bg-gray-100 hover:text-gray-500 focus:bg-gray-100 focus:text-gray-500 focus:outline-none"
            >
              <svg className="h-6 w-6" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                {showingNavigationDropdown ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>
      </div>

      {showingNavigationDropdown && (
        <div className="sm:hidden">
          <div className="space-y-1 pt-2 pb-3">
            <ResponsiveNavLink href={route('admin.tenants.index')} active={route().current('admin.tenants.*')}>
              المستخدمون
            </ResponsiveNavLink>
          </div>

          <div className="border-t border-gray-200 pt-4 pb-1">
            <div className="flex items-center px-4">
              <div className="text-base font-medium text-gray-800">{admin.name}</div>
            </div>

            <div className="mt-3 space-y-1">
              <form
                onSubmit={(e) => {
                  e.preventDefault();
                  post(route('logout'));
                }}
              >
                <ResponsiveNavLink as="button">تسجيل خروج</ResponsiveNavLink>
              </form>
            </div>
          </div>
        </div>
      )}
    </nav>
  );
}
