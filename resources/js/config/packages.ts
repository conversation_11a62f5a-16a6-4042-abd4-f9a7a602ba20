// Global packages configuration
export const packages = [
  {
    id: '100',
    nodes: 100,
    nodesFormatted: '100',
    price: 150,
    priceFormatted: '150',
    pricePerNode: '1.5',
  },
  {
    id: '500',
    nodes: 500,
    nodesFormatted: '500',
    price: 600,
    priceFormatted: '600',
    pricePerNode: '1.2',
    savings: 150,
    savingsFormatted: '150',
  },
  {
    id: '1000',
    nodes: 1000,
    nodesFormatted: '1,000',
    price: 1000,
    priceFormatted: '1,000',
    pricePerNode: '1',
    savings: 500,
    savingsFormatted: '500',
  },
  {
    id: '5000',
    nodes: 5000,
    nodesFormatted: '5,000',
    price: 4000,
    priceFormatted: '4,000',
    pricePerNode: '0.8',
    savings: 3500,
    savingsFormatted: '3,500',
  },
];

export type Package = (typeof packages)[number] & {
  isDiscounted?: boolean;
  originalPrice?: number;
  originalPriceFormatted?: string;
  discountPercentage?: number;
};

// Special discount package for users who reached max nodes
export const discountPackage = {
  id: '50',
  nodes: 50,
  nodesFormatted: '50',
  price: 64, // 75 SAR with 15% discount
  priceFormatted: '64',
  pricePerNode: '1.28',
  isDiscounted: true,
  originalPrice: 75,
  originalPriceFormatted: '75',
  discountPercentage: 15,
};
