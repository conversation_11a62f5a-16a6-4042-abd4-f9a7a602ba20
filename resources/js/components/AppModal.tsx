import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Drawer, DrawerContent } from '@/components/ui/drawer';
import React from 'react';
import { useMedia } from 'react-use';

type Props = {
  open: boolean;
  onOpenChange?: (value: boolean) => void;
  children: React.ReactNode;
};

export default function AppModal({ open, onOpenChange, children }: Props) {
  const isDesktop = useMedia('(min-width: 768px)');

  if (isDesktop) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="bg-sidebar max-h-[80vh] overflow-y-auto">{children}</DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={open} onOpenChange={onOpenChange}>
      <DrawerContent>
        <div className="mt-4 overflow-y-auto">{children}</div>
      </DrawerContent>
    </Drawer>
  );
}
