import { router } from '@inertiajs/react';

type PaginationLink = {
  url: string | null;
  label: string;
  active: boolean;
};

export type PaginationData<T> = {
  current_page: number;
  data: T[];
  first_page_url: string;
  from: number | null;
  last_page: number;
  last_page_url: string;
  links: PaginationLink[];
  next_page_url: string | null;
  path: string;
  per_page: number;
  prev_page_url: string | null;
  to: number | null;
  total: number;
};

type Props<T> = {
  data: PaginationData<T>;
};

export default function Pagination<T>({ data }: Props<T>) {
  const handleClick = (url: string | null) => {
    if (!url) {
      return;
    }

    router.visit(url, {
      preserveScroll: true,
      preserveState: true,
      replace: true,
    });
  };

  return (
    <nav className="mt-6 flex justify-center">
      <ul className="inline-flex space-x-1 rounded-md px-2 py-1">
        {data.links.map((link, index) => (
          <li key={index}>
            <button
              type="button"
              onClick={() => handleClick(link.url)}
              disabled={!link.url}
              className={`rounded px-3 py-1 text-sm ${
                link.active
                  ? 'bg-green-600 text-white'
                  : 'text-gray-700 hover:bg-gray-100 disabled:cursor-not-allowed disabled:text-gray-400'
              }`}
              dangerouslySetInnerHTML={{ __html: link.label }}
            />
          </li>
        ))}
      </ul>
    </nav>
  );
}
