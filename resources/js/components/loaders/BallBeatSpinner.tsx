import { clsx } from 'clsx';

interface BallBeatSpinnerProps {
  color?: string;
  size?: 'xs' | 'sm' | 'md' | 'lg';
  className?: string;
}

export function BallBeatSpinner({ color = '#10B981', size = 'md', className }: BallBeatSpinnerProps) {
  const sizeMap = {
    xs: 4,
    sm: 6,
    md: 8,
    lg: 10,
  };

  const ballSize = sizeMap[size];
  const containerSize = ballSize * 5;

  return (
    <div
      className={clsx('flex items-center justify-center', className)}
      style={{ width: containerSize, height: containerSize }}
    >
      <div className="flex space-x-2">
        {[0, 1, 2].map((i) => (
          <div
            key={i}
            className="animate-pulse rounded-full"
            style={{
              backgroundColor: color,
              width: ballSize,
              height: ballSize,
              animationDelay: `${i * 0.2}s`,
              animationDuration: '0.8s',
            }}
          />
        ))}
      </div>
    </div>
  );
}

export default BallBeatSpinner;
