import { createReactFlowEdge, createReactFlowNode } from '@/components/tree/node-factory';
import { AppNode } from '@/store/tree';
import { NodeModel } from '@/types/models';
import { isNullish, otherParentInfo } from '@/utils/helpers';
import { Edge } from '@xyflow/react';
import { layoutFromMap } from 'entitree-flex';

const Orientation = {
  Vertical: 'vertical',
  Horizontal: 'horizontal',
} as const;

export const nodeWidth = 150;
export const nodeHeight = 36;

type Props = {
  tree: NodeModel[];
  direction?: string;
  preferNodePosition?: boolean;
};

const direction: string = 'TB';

export const calculateLayout = ({ tree, preferNodePosition = false }: Props): { nodes: AppNode[]; edges: Edge[] } => {
  console.log('calculating layout...');
  try {
    const isTreeHorizontal = direction === 'LR';

    const entitreeSettings = {
      clone: true, // returns a copy of the input, if your application does not allow editing the original object
      enableFlex: true, // has slightly better performance if turned off (node.width, node.height will not be read)
      firstDegreeSpacing: isTreeHorizontal ? 40 : -10, // spacing in px between nodes belonging to the same source, e.g. children with same parent
      nextAfterAccessor: 'spouses', // the side node prop used to go sideways, AFTER the current node
      nextAfterSpacing: 50, // the spacing of the "side" nodes AFTER the current node
      nextBeforeAccessor: 'siblings', // the side node prop used to go sideways, BEFORE the current node
      nextBeforeSpacing: 50, // the spacing of the "side" nodes BEFORE the current node
      nodeHeight, // default node height in px
      nodeWidth, // default node width in px
      orientation: Orientation.Vertical, // "vertical" to see parents top and children bottom, "horizontal" to see parents left and
      rootX: 0, // set root position if other than 0
      rootY: 0, // set root position if other than 0
      secondDegreeSpacing: isTreeHorizontal ? 40 : -10, // spacing in px between nodes not belonging to same parent eg "cousin" nodes
      sourcesAccessor: 'parents', // the prop used as the array of ancestors ids
      sourceTargetSpacing: 85, // the "vertical" spacing between nodes in vertical orientation, horizontal otherwise
      targetsAccessor: 'children', // the prop used as the array of children ids
    } as const;

    const root = tree.find((node) => node.is_root)!;

    const { nodes: entitreeNodes, rels: entitreeEdges } = layoutFromMap(root.id, formatTreeData(tree), {
      ...entitreeSettings,
      orientation: isTreeHorizontal ? Orientation.Horizontal : Orientation.Vertical,
    });

    const nodes: AppNode[] = [],
      edges: Edge[] = [];

    entitreeEdges.forEach((edge) => {
      const sourceNode = edge.source.id.toString();
      const targetNode = edge.target.id.toString();
      const childNodeModel = edge.target.data as NodeModel;

      const newEdge = createReactFlowEdge(sourceNode, targetNode, {
        direction: isTreeHorizontal ? 'LR' : 'TB',
        label: childNodeModel.other_parent_relationship
          ? otherParentInfo(childNodeModel.other_parent_relationship)
          : '',
      });

      edges.push(newEdge);
    });

    entitreeNodes.forEach((node) => {
      const nodeModel = node.data as NodeModel;
      const newNode = createReactFlowNode(nodeModel, {
        position: {
          x: preferNodePosition ? (node.data.style?.x ?? node.x) : node.x,
          y: preferNodePosition ? (node.data.style?.y ?? node.y) : node.y,
        },
        direction: isTreeHorizontal ? 'LR' : 'TB',
        isRoot: node.is_root,
        children: node.children,
      });

      nodes.push(newNode);
    });

    return { nodes, edges };
  } catch (e) {
    console.error('Error calculating layout:', e);
    return { nodes: [], edges: [] };
  } finally {
    console.log('layout calculated');
  }
};

const formatTreeData = (
  data: NodeModel[],
): Record<
  number,
  {
    id: number;
    is_root: boolean;
    data: NodeModel;
    children: number[];
  }
> => {
  // First pass: create the base structure and build parent-to-children mapping
  const result: Record<
    number,
    {
      id: number;
      is_root: boolean;
      data: NodeModel;
      children: number[];
    }
  > = {};

  const childrenMap = new Map<number, number[]>();

  // Single pass through data to build both structures
  for (const node of data) {
    // Initialize result entry
    result[node.id] = {
      id: node.id,
      is_root: node.is_root,
      data: node,
      children: [],
    };

    // Build parent-to-children mapping
    if (node.parent_id !== null && node.parent_id !== undefined) {
      if (!childrenMap.has(node.parent_id)) {
        childrenMap.set(node.parent_id, []);
      }
      childrenMap.get(node.parent_id)!.push(node.id);
    }
  }

  // Second pass: assign children arrays and sort by order
  for (const nodeId in result) {
    const id = parseInt(nodeId, 10);
    const childrenIds = childrenMap.get(id) || [];

    // Sort children by order property, null values last
    childrenIds.sort((aId, bId) => {
      const aOrder = result[aId].data.order;
      const bOrder = result[bId].data.order;

      // If both have null order, maintain original order
      if (isNullish(aOrder) && isNullish(bOrder)) return 0;
      // If a is null, put it after b
      if (isNullish(aOrder)) return 1;
      // If b is null, put it after a
      if (isNullish(bOrder)) return -1;
      // Both have values, sort numerically
      return aOrder - bOrder;
    });

    result[id].children = childrenIds;
  }

  return result;
};
