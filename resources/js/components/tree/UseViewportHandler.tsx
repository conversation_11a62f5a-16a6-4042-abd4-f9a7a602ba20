import { useTreeStore } from '@/store/tree';
import { NodeModel } from '@/types/models';
import { FitViewOptions, useOnViewportChange, useReactFlow, Viewport } from '@xyflow/react';
import { useEffect, useRef } from 'react';
import { useLocalStorage } from 'react-use';

export const VIEWPORT_STORAGE_KEY = 'family-tree-viewport';

export const fitViewOptions: FitViewOptions = {
  maxZoom: 1,
  minZoom: 0.5,
  duration: 800,
};

type ViewportHandlerProps = {
  initialNode?: NodeModel;
};

export const useViewportHandler = ({ initialNode }: ViewportHandlerProps) => {
  const nodes = useTreeStore((state) => state.nodes);
  const { setViewport, fitView } = useReactFlow();
  const [savedViewport, setSavedViewport] = useLocalStorage<Viewport | null>(VIEWPORT_STORAGE_KEY, null);
  const hasSetInitialViewport = useRef(false);

  useOnViewportChange({
    onEnd: (viewport) => {
      if (hasSetInitialViewport.current) {
        setSavedViewport(viewport);
      }
    },
  });

  useEffect(() => {
    if (!nodes || hasSetInitialViewport.current) {
      return;
    }

    hasSetInitialViewport.current = true;

    if (initialNode) {
      fitView({ ...fitViewOptions, nodes: [{ id: initialNode.id.toString() }] });
      return;
    }

    if (savedViewport) {
      setViewport(savedViewport);
      return;
    }
  }, [fitView, initialNode, savedViewport, setViewport, nodes]);
};
