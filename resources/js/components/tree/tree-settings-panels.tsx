import { Panel } from '@xyflow/react';

import AppModal from '@/components/AppModal';
import SubHeader from '@/components/typography/SubHeader';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { TreeSettings } from '@/Pages/Tenant/Branch/Show';
import { SettingsIcon } from '@hugeicons-pro/core-stroke-standard';
import { HugeiconsIcon } from '@hugeicons/react';
import { useForm, usePage } from '@inertiajs/react';
import { useState } from 'react';

export const TreeSettingsPanel = () => {
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);

  const settings = usePage().props.settings as TreeSettings;

  const form = useForm({
    layout_mode: settings.layout_mode,
  });

  return (
    <>
      <Panel
        position="top-left"
        className="text-foreground bg-sidebar !-top-3 !-left-3 mt-2 flex cursor-pointer items-center gap-1 rounded-lg border px-2 py-1.5"
        onClick={() => setIsSettingsOpen(true)}
      >
        <span className="text-sm font-medium">اعدادات الشجرة</span>
        <HugeiconsIcon size={18} icon={SettingsIcon} style={{ cursor: 'pointer' }} />
      </Panel>

      <AppModal open={isSettingsOpen} onOpenChange={setIsSettingsOpen}>
        <SubHeader className="mt-4">اعدادات الشجرة</SubHeader>

        <div className="space-y-6">
          {/* Layout Mode Section */}
          <Tabs
            value={settings.layout_mode}
            onValueChange={(value) => {
              form.setData('layout_mode', value as 'auto' | 'manual');
              form.post(route('settings.general.update'), {
                preserveScroll: true,
                onSuccess: () => setIsSettingsOpen(false),
              });
            }}
          >
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="auto">تلقائي</TabsTrigger>
              <TabsTrigger value="manual">يدوي</TabsTrigger>
            </TabsList>

            <TabsContent value="auto">
              <p className="text-muted-foreground text-center text-sm">ترتيب تلقائي للأفراد ولا يمكن سحبهم</p>
            </TabsContent>

            <TabsContent value="manual">
              <p className="text-muted-foreground text-center text-sm">يمكن سحب الأفراد وترتيبهم يدوياً</p>
            </TabsContent>
          </Tabs>
        </div>
      </AppModal>
    </>
  );
};
