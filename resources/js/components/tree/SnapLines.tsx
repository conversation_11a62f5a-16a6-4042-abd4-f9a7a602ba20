import { SnapLine } from '@/hooks/use-node-snapping';
import { useViewport } from '@xyflow/react';

type SnapLinesProps = {
  snapLines: SnapLine[];
};

export const SnapLines = ({ snapLines }: SnapLinesProps) => {
  const { x, y, zoom } = useViewport();

  if (snapLines.length === 0) return null;

  return (
    <>
      {snapLines.map((line) => (
        <div
          key={line.id}
          style={{
            position: 'absolute',
            left:
              line.orientation === 'vertical'
                ? `${line.x1 * zoom + x - 1}px`
                : `${Math.min(line.x1, line.x2) * zoom + x}px`,
            top:
              line.orientation === 'horizontal'
                ? `${line.y1 * zoom + y - 1}px`
                : `${Math.min(line.y1, line.y2) * zoom + y}px`,
            width: line.orientation === 'vertical' ? '1px' : `${Math.abs(line.x2 - line.x1) * zoom}px`,
            height: line.orientation === 'horizontal' ? '1px' : `${Math.abs(line.y2 - line.y1) * zoom}px`,
            backgroundColor: 'transparent',
            borderLeft: line.orientation === 'vertical' ? '1px dashed #9CA3AF' : 'none',
            borderTop: line.orientation === 'horizontal' ? '1px dashed #9CA3AF' : 'none',
            opacity: 0.8,
            pointerEvents: 'none',
            zIndex: 1000,
          }}
        />
      ))}
    </>
  );
};
