import { BaseNode } from '@/components/base-node';
import ColorPicker from '@/components/ColorPicker';
import {
  NodeHeader,
  NodeHeaderActions,
  NodeHeaderIcon,
  NodeHeaderMenuAction,
  NodeHeaderTitle,
} from '@/components/node-header';
import ScaleTransition from '@/components/ScaleTransition';
import { Button } from '@/components/ui/button';
import { DropdownMenuItem } from '@/components/ui/dropdown-menu';
import { useHistoryActions } from '@/hooks/useHistoryActions';
import { cn } from '@/lib/utils';
import { NodeModalType, useNodeModalStore } from '@/store/node-modal';
import { AppNode, useTreeStore } from '@/store/tree';
import { NodeModel } from '@/types/models';
import { Female02Icon, Male02Icon, PlusSignIcon, ViewIcon } from '@hugeicons-pro/core-stroke-standard';
import { HugeiconsIcon } from '@hugeicons/react';
import { router } from '@inertiajs/react';
import { <PERSON>le, NodeProps, Position, useStore } from '@xyflow/react';
import { memo, useCallback } from 'react';
import fastMemo from 'react-fast-memo';

export const nodeTypes = {
  baseNode: memo((props: NodeProps<AppNode>) => {
    const nodesSettings = useTreeStore((s) => s.nodesSettings);

    const showContent = useStore((s) => s.transform[2] >= 0.4);
    const { appendToHistory } = useHistoryActions();

    const setNodeModal = useNodeModalStore((s) => s.setNodeModal);

    const node = props.data.nodeModel;

    const isTreeHorizontal = props.data.direction === 'LR';

    const isRootNode = props.data?.isRoot;

    const onClickAdd = useCallback(() => setNodeModal(NodeModalType.NODE_INFO, node), [setNodeModal, node]);

    return (
      <BaseNode
        selected={props.selected}
        onClick={(e) => {
          if (nodesSettings.onClickNode) {
            e.stopPropagation();
            nodesSettings.onClickNode(node);
          }
        }}
        className="relative px-2 py-1 pb-0"
        dir="rtl"
        style={{
          borderTop: showContent ? `4px solid ${node.bg_color}` : `50px solid ${node.bg_color}`,
          boxShadow: '0 5px 15px rgba(0,0,0,0.08), 0 2px 5px rgba(0,0,0,0.05)',
        }}
      >
        {/* Glow effect */}
        <div
          className="pointer-events-none absolute inset-0 opacity-10"
          style={{
            background: `radial-gradient(ellipse at center, ${node.bg_color} 0%, transparent 100%)`,
          }}
        />
        {showContent ? <NodeContent node={node} editable={nodesSettings.editable} /> : null}

        <Handle
          className="invisible"
          type="source"
          position={isTreeHorizontal ? Position.Right : Position.Bottom}
          id={isTreeHorizontal ? Position.Right : Position.Bottom}
        />

        {props.selected && nodesSettings.editable ? (
          <ScaleTransition>
            <div className="flex flex-col items-center gap-2 px-3 py-2">
              <div>
                <ColorPicker
                  value={node.bg_color}
                  onSelect={(color) => {
                    appendToHistory({ type: 'recolor-node', node: node, payload: color });
                  }}
                />
              </div>
            </div>
          </ScaleTransition>
        ) : null}

        {((isRootNode && props.data.children?.length === 0) || props.selected) &&
          showContent &&
          nodesSettings.editable && (
            <ScaleTransition>
              <div className="flex justify-center">
                <Button size="xs" className="mb-2 w-full" onClick={onClickAdd}>
                  <HugeiconsIcon icon={PlusSignIcon} size={8} />
                  <span className="text-[9px]">اضافة فرد</span>
                </Button>
              </div>
            </ScaleTransition>
          )}

        {/* Target Handle */}
        {!isRootNode && (
          <Handle
            className="invisible"
            type="target"
            position={isTreeHorizontal ? Position.Left : Position.Top}
            id={isTreeHorizontal ? Position.Left : Position.Top}
          />
        )}
      </BaseNode>
    );
  }),
};

type NodeContentProps = {
  node: NodeModel;
  onClickNode?: (node: NodeModel) => void;
  editable?: boolean;
};

export const NodeContent = fastMemo(({ node, onClickNode, editable }: NodeContentProps) => {
  const removeNode = useTreeStore((s) => s.removeNode);
  const setNodeModal = useNodeModalStore((s) => s.setNodeModal);

  let textSize = 10;
  if (node.name.length > 7) {
    textSize = 9;
  }

  const onClickView = useCallback(
    () => onClickNode?.(node) ?? setNodeModal(NodeModalType.NODE_INFO, node),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [setNodeModal, node],
  );

  const onClickRemove = useCallback(() => {
    if (!confirm('هل أنت متأكد؟')) {
      return;
    }

    router.delete(route('nodes.destroy', node.id), {
      onSuccess: () => removeNode(node.id.toString()),
    });
  }, [node.id, removeNode]);

  return (
    <NodeHeader>
      <NodeHeaderIcon>
        <div className="flex flex-col items-center">
          <div className="grow">
            {node.gender === 'male' ? (
              <HugeiconsIcon size={12} icon={Male02Icon} className="text-blue-500" />
            ) : (
              <HugeiconsIcon size={12} icon={Female02Icon} className="text-pink-500" />
            )}
          </div>
          <div
            className={cn('mt-0.5 rounded border-[0.5px] px-[2px] py-[1px] text-[6px] font-semibold', {
              'border-green-600 text-green-600': node.life_status === 'alive',
              'border-gray-600 text-gray-600': node.life_status === 'dead',
            })}
          >
            {node.life_status === 'alive' ? <span>حي</span> : node.life_status === 'dead' ? <span>ميت</span> : null}
          </div>
        </div>
      </NodeHeaderIcon>
      <NodeHeaderTitle className="w-full truncate text-nowrap" style={{ fontSize: `${textSize}px` }}>
        {node.name}
      </NodeHeaderTitle>
      <NodeHeaderActions>
        <Button variant="ghost" size="icon" onClick={onClickView} className="-mx-2">
          <div className="flex flex-col justify-center">
            <HugeiconsIcon size={14} icon={ViewIcon} className="text-gray-600" />
            <span className="-mt-0.5 text-[5.5px] font-semibold text-gray-600">عرض</span>
          </div>
        </Button>
        {editable ? (
          <NodeHeaderMenuAction label="">
            <DropdownMenuItem variant="destructive" onClick={onClickRemove}>
              حذف
            </DropdownMenuItem>
          </NodeHeaderMenuAction>
        ) : null}
      </NodeHeaderActions>
    </NodeHeader>
  );
});
