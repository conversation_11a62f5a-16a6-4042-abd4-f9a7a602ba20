import { forwardRef } from 'react';

import { Panel, PanelProps } from '@xyflow/react';

import { cn } from '@/lib/utils';
import { Search01Icon } from '@hugeicons-pro/core-stroke-standard';
import { HugeiconsIcon } from '@hugeicons/react';

export const SearchTree = forwardRef<
  HTMLDivElement,
  Omit<PanelProps, 'children'> & {
    onClickSearch?: () => void;
  }
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
>(({ className, onClickSearch, ...props }, ref) => {
  return (
    <Panel
      position="top-right"
      onClick={() => onClickSearch?.()}
      className={cn(
        'text-foreground bg-sidebar !-top-3 !-right-3 flex cursor-pointer items-center gap-1 rounded-lg border px-2 py-1.5',
        className,
      )}
      {...props}
    >
      <span className="text-sm font-medium">بحث</span>
      <HugeiconsIcon size={18} icon={Search01Icon} style={{ cursor: 'pointer' }} />
    </Panel>
  );
});

SearchTree.displayName = 'SearchTree';
