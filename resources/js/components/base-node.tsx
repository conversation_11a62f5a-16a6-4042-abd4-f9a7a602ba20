import { forwardRef, HTMLAttributes } from 'react';

import { cn } from '@/lib/utils';

export const BaseNode = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement> & { selected?: boolean }>(
  ({ className, selected, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(
        'bg-card text-card-foreground relative w-36 rounded-md transition-colors',
        'outline-border outline-1 hover:outline-gray-500/50',
        className,
        selected ? 'shadow-lg outline-2 outline-gray-300' : '',
        'ring-gray-500/20 hover:ring-2',
      )}
      tabIndex={0}
      {...props}
    >
      {props.children}
    </div>
  ),
);

BaseNode.displayName = 'BaseNode';
