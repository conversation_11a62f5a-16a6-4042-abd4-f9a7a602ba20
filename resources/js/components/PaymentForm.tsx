import ContactUsMobile from '@/components/ContactUsMobile';
import type { Package } from '@/config/packages';
import { useEffect } from 'react';

interface Props {
  pkg: Package;
}

export default function PaymentPanel({ pkg }: Props) {
  useEffect(() => {
    window.Moyasar.init({
      element: document.getElementById('payment-form'),
      amount: pkg.price * 100, // Convert to halala
      currency: 'SAR',
      description: `باقة شجرة العائلة - ${pkg.nodes} فرد`,
      publishable_api_key: import.meta.env.VITE_MOYASAR_PUBLISHABLE_KEY,
      callback_url: route('payments.verify'),
      metadata: {
        package_id: pkg.id,
      },
      methods: ['creditcard', 'applepay'],
      apple_pay: {
        country: 'SA',
        label: `باقة شجرة العائلة - ${pkg.nodes} فرد`,
        validate_merchant_url: 'https://api.moyasar.com/v1/applepay/initiate',
        supported_countries: [
          'SA', // السعودية
          'AE', // الإمارات العربية المتحدة
          'KW', // الكويت
          'BH', // البحرين
          'QA', // قطر
          'OM', // سُلطنة عُمان
          'EG', // مصر
          'JO', // الأردن
          'LB', // لبنان
          'IQ', // العراق
          'TR', // تركيا
          'US', // الولايات المتحدة الأمريكية
          'GB', // المملكة المتحدة
        ],
      },
    });
  }, [pkg]);

  return (
    <div className="p-4">
      <h3 className="mb-4 text-lg font-bold">الدفع</h3>
      <div className="mb-4">
        <div className="rounded-lg bg-gray-50 p-3">
          <p className="font-semibold">تفاصيل الباقة:</p>
          <ul className="mt-2 text-sm text-gray-600">
            <li>عدد الأفراد: {pkg.nodes}</li>
            {pkg.isDiscounted ? (
              <li className="flex items-center gap-2">
                <span>السعر:</span>
                <span className="text-gray-500 line-through">{pkg.originalPrice} ريال</span>
                <span className="font-bold text-green-600">{pkg.price} ريال</span>
                <span className="rounded-full bg-green-100 px-2 py-0.5 text-xs font-bold text-green-700">خصم 15%</span>
              </li>
            ) : (
              <li>السعر: {pkg.price} ريال</li>
            )}
          </ul>
        </div>
      </div>

      <div id="payment-form" />

      <div className="mt-6 rounded-lg border border-amber-200 bg-amber-50 p-4">
        <h4 className="mb-2 font-semibold text-amber-800">التحويل البنكي</h4>
        <p className="text-sm text-amber-700">
          إذا لم تتمكن من الدفع بالطرق أعلاه، يمكنك تحويل المبلغ ({pkg.price} ريال) ثم التواصل معنا على الرقم{' '}
          <ContactUsMobile /> مع ذكر اسم العائلة.
        </p>
        <div className="mt-3 rounded border border-amber-300 bg-white p-2 text-sm">
          <p className="font-bold">معلومات الحساب:</p>
          التحويل: (مؤسسة أوراق لتقنية المعلومات)
          <br />
          مصرف الراجحي: رقم الحساب: 660608016059744
          <br />
          الآيبان: SA5880000660608016059744
        </div>
      </div>
    </div>
  );
}
