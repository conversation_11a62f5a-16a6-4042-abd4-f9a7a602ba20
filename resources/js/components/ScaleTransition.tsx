import { AnimatePresence, motion } from 'framer-motion';
import { useEffect, useState } from 'react';

interface ScaleTransitionProps {
  children: React.ReactNode;
}

export const ScaleTransition = ({ children }: ScaleTransitionProps) => {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  return (
    <AnimatePresence>
      {isMounted && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          transition={{ duration: 0.2 }}
        >
          {children}
        </motion.div>
      )}
    </AnimatePresence>
  );
};

// Using named export as per the guidelines
export default ScaleTransition;
