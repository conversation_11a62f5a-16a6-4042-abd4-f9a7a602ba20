import { Toaster as Sonner, ToasterProps } from 'sonner';

const Toaster = ({ ...props }: ToasterProps) => {
  return (
    <Sonner
      className="toaster group"
      toastOptions={{
        classNames: {
          toast: 'font-sans',
          title: 'font-sans',
          success: '!text-green-600',
          error: '!text-red-600',
          description: 'font-sans',
          actionButton: 'font-sans',
          cancelButton: 'font-sans',
          closeButton: 'font-sans',
        },
      }}
      {...props}
    />
  );
};

export { Toaster };
