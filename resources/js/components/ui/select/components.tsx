import { clsx } from 'clsx';

export const controlStyles = {
  base: 'border rounded-md bg-transparent hover:cursor-pointer min-w-[10rem] h-9 ps-3 pe-1 text-base md:text-sm',
  focus: 'focus-visible:border-ring focus-visible:ring-ring/25 focus-visible:ring-[2.5px]',
  nonFocus: 'border-input hover:border-input',
  disabled: 'disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50',
};
export const placeholderStyles = 'placeholder:text-muted-foreground text-base md:text-sm';
export const selectInputStyles = 'text-base md:text-sm';
export const valueContainerStyles = 'gap-1';
export const singleValueStyles = 'text-base md:text-sm';
export const multiValueStyles = 'rounded items-center py-0.5 ps-2 pe-1 gap-1.5 bg-muted';
export const multiValueLabelStyles = 'text-base md:text-sm';
export const multiValueRemoveStyles =
  'border border-input bg-transparent hover:bg-accent hover:text-accent-foreground text-muted-foreground rounded-md';
export const indicatorsContainerStyles = 'gap-1';
export const clearIndicatorStyles = 'text-muted-foreground p-1 rounded-md hover:bg-accent hover:text-accent-foreground';
export const indicatorSeparatorStyles = 'bg-input';
export const dropdownIndicatorStyles =
  'p-1 hover:bg-accent text-muted-foreground justify-center rounded-md hover:text-foreground';
export const menuStyles = 'p-1 mt-2 border border-input bg-background rounded-md shadow-md';
export const groupHeadingStyles = 'ms-3 mt-2 mb-1 text-muted-foreground text-sm';
export const optionStyles = {
  base: 'hover:cursor-pointer px-3 py-1 rounded-sm text-base md:text-sm',
  focus: 'bg-accent text-accent-foreground',
  selected: 'text-primary',
};
export const noOptionsMessageStyles = 'text-muted-foreground p-2 bg-muted border border-dashed border-input rounded-sm';

export const selectClasses = {
  control: ({ isFocused, isDisabled }: { isFocused: boolean; isDisabled: boolean }) =>
    clsx(
      isFocused ? controlStyles.focus : controlStyles.nonFocus,
      isDisabled ? controlStyles.disabled : '',
      controlStyles.base,
      'transition-[color,box-shadow] outline-none',
      isFocused &&
        'aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive',
    ),
  placeholder: () => placeholderStyles,
  input: () => selectInputStyles,
  valueContainer: () => valueContainerStyles,
  singleValue: () => singleValueStyles,
  multiValue: () => multiValueStyles,
  multiValueLabel: () => multiValueLabelStyles,
  multiValueRemove: () => multiValueRemoveStyles,
  indicatorsContainer: () => indicatorsContainerStyles,
  clearIndicator: () => clearIndicatorStyles,
  indicatorSeparator: () => indicatorSeparatorStyles,
  dropdownIndicator: () => dropdownIndicatorStyles,
  menu: () => menuStyles,
  groupHeading: () => groupHeadingStyles,
  option: ({ isFocused, isSelected }: { isFocused: boolean; isSelected: boolean }) =>
    clsx(isFocused && optionStyles.focus, isSelected && optionStyles.selected, optionStyles.base),
  noOptionsMessage: () => noOptionsMessageStyles,
};

export type OptionType = {
  label?: string;
  name?: string;
  value?: string | number;
  id?: string | number;
  [key: string]: unknown;
};
