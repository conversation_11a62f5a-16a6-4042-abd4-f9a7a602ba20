import { Label } from '@/components/ui/label';
import { createSelectComponents } from '@/components/ui/select/selectComponents';
import { GroupBase, SelectComponentsConfig } from 'react-select';
import { AsyncProps } from 'react-select/async';
import { default as BaseSelect } from 'react-select/async-creatable';
import { useDebouncedCallback } from 'use-debounce';
import { OptionType, selectClasses } from './components';

const selectComponents = createSelectComponents<OptionType, boolean, GroupBase<OptionType>>();

interface CustomAsyncCreatableSelectProps<
  Option,
  IsMulti extends boolean = false,
  Group extends GroupBase<Option> = GroupBase<Option>,
> extends AsyncProps<Option, IsMulti, Group> {
  label?: string;
  error?: string;
  containerClassName?: string;
}

export const AsyncCreatableSelect = <
  Option,
  IsMulti extends boolean = false,
  Group extends GroupBase<Option> = GroupBase<Option>,
>({
  loadOptions = () => {},
  label,
  error,
  containerClassName,
  ...props
}: CustomAsyncCreatableSelectProps<Option, IsMulti, Group>) => {
  const debounced = useDebouncedCallback(loadOptions, 500, { leading: true });

  return (
    <div className={containerClassName}>
      {label && <Label htmlFor="name">{label}</Label>}
      <BaseSelect
        className="mt-2"
        cacheOptions
        defaultOptions
        loadOptions={debounced}
        getOptionLabel={(option: Option) => (option as OptionType).label ?? (option as OptionType).name ?? ''}
        getOptionValue={(option: Option) =>
          ((option as OptionType).value ?? (option as OptionType).id ?? '').toString()
        }
        loadingMessage={() => 'تحميل...'}
        noOptionsMessage={() => 'لا يوجد خيارات'}
        placeholder="اختر"
        isValidNewOption={() => false}
        unstyled
        styles={{
          input: (base) => ({ ...base, 'input:focus': { boxShadow: 'none' } }),
          // On mobile, the label will truncate automatically, so we want to
          // override that behaviour.
          multiValueLabel: (base) => ({ ...base, whiteSpace: 'normal', overflow: 'visible' }),
          control: (base) => ({ ...base, transition: 'none' }),
        }}
        components={selectComponents as SelectComponentsConfig<Option, IsMulti, Group>}
        classNames={selectClasses}
        {...props}
      />
      <div className="mt-2 text-sm text-red-500">{error}</div>
    </div>
  );
};
