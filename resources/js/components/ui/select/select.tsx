import { Label } from '@/components/ui/label';
import { OptionType, selectClasses } from '@/components/ui/select/components';
import { createSelectComponents } from '@/components/ui/select/selectComponents';
import { default as BaseSelect, GroupBase, SelectComponentsConfig } from 'react-select';
import { AsyncProps } from 'react-select/async';

const selectComponents = createSelectComponents<OptionType, boolean, GroupBase<OptionType>>();

interface CustomSelectProps<
  Option,
  IsMulti extends boolean = false,
  Group extends GroupBase<Option> = GroupBase<Option>,
> extends AsyncProps<Option, IsMulti, Group> {
  label?: string;
  error?: string;
  containerClassName?: string;
}

export const Select = <
  Option = OptionType,
  IsMulti extends boolean = false,
  Group extends GroupBase<Option> = GroupBase<Option>,
>({
  label,
  error,
  containerClassName,
  ...props
}: CustomSelectProps<Option, IsMulti, Group>) => {
  return (
    <div className={containerClassName}>
      {label && <Label>{label}</Label>}
      <div className="mt-2">
        <BaseSelect<Option, IsMulti, Group>
          getOptionLabel={(option: Option) => (option as OptionType).label ?? (option as OptionType).name ?? ''}
          getOptionValue={(option: Option) =>
            ((option as OptionType).value ?? (option as OptionType).id ?? '').toString()
          }
          loadingMessage={() => 'تحميل...'}
          noOptionsMessage={() => 'لا يوجد خيارات'}
          placeholder="اختر"
          unstyled
          styles={{
            input: (base) => ({ ...base, 'input:focus': { boxShadow: 'none' } }),
            multiValueLabel: (base) => ({ ...base, whiteSpace: 'normal', overflow: 'visible' }),
            control: (base) => ({ ...base, transition: 'none' }),
          }}
          components={selectComponents as SelectComponentsConfig<Option, IsMulti, Group>}
          classNames={selectClasses}
          {...props}
        />
        <div className="mt-2 text-sm text-red-500">{error}</div>
      </div>
    </div>
  );
};
