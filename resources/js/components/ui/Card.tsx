import SubHeader from '@/components/typography/SubHeader';
import { Box } from '@/components/ui/box';
import { ReactNode } from 'react';

type CardProps = {
  title?: string;
  children: ReactNode;
  topEnd?: ReactNode;
  className?: string;
  containerClassName?: string;
};

export default function Card({ title, topEnd, children, containerClassName, className }: CardProps) {
  return (
    <Box className={containerClassName}>
      <div className="flex justify-between">
        {title ? <SubHeader className="mb-2.5">{title}</SubHeader> : null}
        {topEnd}
      </div>
      <div className={className}>{children}</div>
    </Box>
  );
}
