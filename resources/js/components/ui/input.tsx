import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import * as React from 'react';

export type InputProps = React.InputHTMLAttributes<HTMLInputElement> & {
  label?: string;
  error?: string;
  suffix?: React.ReactNode | null;
  helper?: string;
};

function Input({ className, type, label, error, suffix, helper, ...props }: InputProps) {
  const id = React.useId();

  return (
    <div className="w-full">
      {label && (
        <Label htmlFor={id} className="mb-2 block">
          {label}
        </Label>
      )}
      <div className="relative">
        {suffix && <div className="absolute inset-y-0 left-0 flex items-center pl-3">{suffix}</div>}
        <input
          id={id}
          type={type}
          data-slot="input"
          className={cn(
            'border-input file:text-foreground selection:bg-primary selection:text-primary-foreground flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-400/75 disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',
            'focus-visible:border-ring focus-visible:ring-ring/25 focus-visible:ring-[2.5px]',
            'aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive',
            suffix && 'pl-10',
            className,
          )}
          aria-invalid={!!error}
          {...props}
        />
      </div>
      {error && <p className="mt-1 text-sm text-red-500">{error}</p>}
      {helper && <p className="mt-1 text-xs text-gray-500">{helper}</p>}
    </div>
  );
}

export { Input };
