import { Cancel01Icon } from '@hugeicons-pro/core-stroke-standard';
import { HugeiconsIcon } from '@hugeicons/react';
import * as DialogPrimitive from '@radix-ui/react-dialog';
import * as React from 'react';

import { cn } from '@/lib/utils';

interface ZoomProps {
  children: React.ReactElement<React.ImgHTMLAttributes<HTMLImageElement>>;
  className?: string;
}

export function Zoom({ children, className }: ZoomProps) {
  const [open, setOpen] = React.useState(false);

  // Clone the child image element to add click handler
  const trigger = React.cloneElement(children, {
    ...children.props,
    className: cn(children.props.className, 'cursor-zoom-in', className),
    onClick: (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation(); // Prevent event bubbling to parent dialog
      setOpen(true);
      // Call original onClick if it exists
      children.props.onClick?.(e);
    },
  });

  return (
    <DialogPrimitive.Root open={open} onOpenChange={setOpen}>
      {trigger}
      <DialogPrimitive.Portal>
        <DialogPrimitive.Overlay className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-[9999] bg-black/90" />
        <DialogPrimitive.Content
          className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed inset-0 z-[10000] flex items-center justify-center p-4"
          onOpenAutoFocus={(e) => e.preventDefault()}
          onPointerDownOutside={(e) => {
            e.preventDefault();
            setOpen(false);
          }}
          onEscapeKeyDown={(e) => {
            e.preventDefault();
            e.stopPropagation(); // Prevent escape from closing parent dialog
            setOpen(false);
          }}
          onClick={(e) => {
            // Only close if clicking on the backdrop (not the image)
            if (e.target === e.currentTarget) {
              e.preventDefault();
              e.stopPropagation();
              setOpen(false);
            }
          }}
        >
          {/* Close button */}
          <DialogPrimitive.Close className="absolute top-4 right-4 z-10 rounded-full bg-black/50 p-2 text-white transition-colors hover:bg-black/70 focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-black focus:outline-none">
            <HugeiconsIcon icon={Cancel01Icon} size={20} strokeWidth={2} className="text-white" />
            <span className="sr-only">إغلاق</span>
          </DialogPrimitive.Close>

          {/* Zoomed image */}
          <div
            className="relative max-h-full max-w-full"
            onClick={(e) => e.stopPropagation()} // Prevent image container clicks from closing
          >
            <img
              src={children.props.src}
              alt={children.props.alt}
              className="max-h-[90vh] max-w-[90vw] object-contain"
              style={{
                cursor: 'zoom-out',
              }}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                setOpen(false);
              }}
            />
          </div>
        </DialogPrimitive.Content>
      </DialogPrimitive.Portal>
    </DialogPrimitive.Root>
  );
}
