import { Link } from '@inertiajs/react';
import clsx from 'clsx';
import { ReactNode } from 'react';

type ResponsiveNavLinkProps = {
  as?: 'a' | 'button';
  href?: string;
  active?: boolean;
  children: ReactNode;
};

export default function ResponsiveNavLink({ as = 'a', href = '#', active = false, children }: ResponsiveNavLinkProps) {
  const classes = clsx(
    'block w-full border-l-4 py-2 pr-4 pl-3 text-base font-medium transition duration-150 ease-in-out',
    active
      ? 'border-green-400 bg-green-50 text-green-700 focus:border-green-700 focus:bg-green-100 focus:text-green-800'
      : 'border-transparent text-gray-600 hover:border-gray-300 hover:bg-gray-50 hover:text-gray-800 focus:border-gray-300 focus:bg-gray-50 focus:text-gray-800',
  );

  if (as === 'button') {
    return <button className={classes}>{children}</button>;
  }

  return (
    <Link href={href} className={classes}>
      {children}
    </Link>
  );
}
