import Helper from '@/components/typography/Helper';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { clsx } from 'clsx';
import { useEffect, useState } from 'react';

interface HijriDate {
  year: number;
  month: number;
  day: number;
}

interface HijriCalendarProps {
  value: string;
  onChange: (value: string) => void;
  label: string;
  error?: string;
  className?: string;
}

const split = (dateAsString?: string): HijriDate | null => {
  if (!dateAsString) {
    return null;
  }

  const array = dateAsString.split('/');

  if (array.length !== 3) {
    return null;
  }

  return {
    year: parseInt(array[0]),
    month: parseInt(array[1]),
    day: parseInt(array[2]),
  };
};

export function HijriCalendar({ onChange, value, label, error, className }: HijriCalendarProps) {
  const [selectedDay, setSelectedDay] = useState('');
  const [selectedMonth, setSelectedMonth] = useState('');
  const [selectedYear, setSelectedYear] = useState('');

  useEffect(() => {
    if (selectedDay && selectedMonth && selectedYear) {
      onChange(`${selectedYear}/${selectedMonth}/${selectedDay}`);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedDay, selectedMonth, selectedYear]);

  useEffect(() => {
    const hijri = split(value);

    if (hijri) {
      setSelectedDay(hijri.day.toString());
      setSelectedMonth(hijri.month.toString());
      setSelectedYear(hijri.year.toString());
    }
  }, [value]);

  return (
    <div className={className}>
      <Label className="mb-2">{label}</Label>
      <div className="flex gap-x-2">
        <Input
          value={selectedDay}
          onChange={(e) => setSelectedDay(e.target.value)}
          maxLength={2}
          min="1"
          max="30"
          pattern="\d*"
          placeholder="1"
          aria-label="يوم"
        />
        <Input
          value={selectedMonth}
          onChange={(e) => setSelectedMonth(e.target.value)}
          maxLength={2}
          min="1"
          max="12"
          placeholder="1"
          pattern="\d*"
          aria-label="شهر"
        />
        <Input
          value={selectedYear}
          onChange={(e) => setSelectedYear(e.target.value)}
          maxLength={4}
          min="1"
          max="1500"
          placeholder="1400"
          pattern="\d*"
          aria-label="سنة"
        />
      </div>
      {error && <p className={clsx('mt-2 text-sm text-red-500')}>{error}</p>}
      <Helper className="mt-1">التاريخ الهجري</Helper>
    </div>
  );
}

export default HijriCalendar;
