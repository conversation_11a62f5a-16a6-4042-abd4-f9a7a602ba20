import { InertiaFormProps, usePage } from '@inertiajs/react';

type ValidationErrorsProps = {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  form?: InertiaFormProps<any>;
};

export default function ValidationErrors({ form }: ValidationErrorsProps) {
  const page = usePage().props;
  const errors = form?.errors ?? page.errors;
  const hasErrors = errors && Object.keys(errors).length > 0 && Object.values(errors).some(Boolean);

  if (!hasErrors) {
    return null;
  }

  return (
    <div className="rounded-lg border border-red-100 bg-red-50 p-2">
      <ul className="list-inside list-none text-xs text-red-500">
        {Object.entries(errors).map(([key, error]) => (
          <li key={key}>{error as string}</li>
        ))}
      </ul>
    </div>
  );
}
