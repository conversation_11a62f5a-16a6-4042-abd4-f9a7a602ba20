import Logo from '@/components/Logo';
import { Button } from '@/components/ui/button';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarTrigger,
  useSidebar,
} from '@/components/ui/sidebar';
import useUser from '@/hooks/useUser';
import {
  CalendarIcon,
  Chart01Icon,
  Chart03Icon,
  HelpCircleIcon,
  MailAdd01Icon,
  MailEdit01Icon,
  SettingsIcon,
  TableIcon,
  TreeIcon,
  User02Icon,
  UserGroup02Icon,
} from '@hugeicons-pro/core-stroke-standard';
import { HugeiconsIcon } from '@hugeicons/react';
import { Link, router } from '@inertiajs/react';
import { useQueryClient } from '@tanstack/react-query';
import { memo } from 'react';

const AppSidebar = memo(() => {
  const queryClient = useQueryClient();
  const user = useUser()!;
  const sidebar = useSidebar();

  return (
    <Sidebar collapsible="icon" variant="sidebar" side="right">
      <SidebarContent>
        {sidebar.open ? null : (
          <SidebarGroup className="ms-0.5 mt-4">
            <SidebarGroupContent>
              <SidebarTrigger className="place-self-center" />
            </SidebarGroupContent>
          </SidebarGroup>
        )}

        {/* Logo Group */}
        {sidebar.open ? (
          <SidebarGroup>
            <SidebarGroupLabel className="mt-2 mb-8 flex items-center justify-between">
              <Link href={route('branches.show')}>
                <Logo width={48} height={48} className="text-green-600" />
              </Link>
              <SidebarTrigger />
            </SidebarGroupLabel>
          </SidebarGroup>
        ) : null}

        {/* Tree Management Group */}
        <SidebarGroup>
          <SidebarGroupLabel>إدارة الشجرة</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {treeManagementItems.map((item) => (
                <SidebarMenuItem key={item.label}>
                  <SidebarMenuButton asChild isActive={route().current(item.active)}>
                    <Link href={item.url}>
                      <HugeiconsIcon icon={item.icon} size={24} />
                      <span className="font-medium">{item.label}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* Family Content Group */}
        <SidebarGroup>
          <SidebarGroupLabel>محتوى العائلة</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {familyContentItems.map((item) => (
                <SidebarMenuItem key={item.label}>
                  <SidebarMenuButton asChild isActive={route().current(item.active)}>
                    <Link href={item.url}>
                      <HugeiconsIcon icon={item.icon} size={24} />
                      <span className="font-medium">{item.label}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* Requests Group */}
        <SidebarGroup>
          <SidebarGroupLabel>الطلبات</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {requestsItems.map((item) => (
                <SidebarMenuItem key={item.label}>
                  <SidebarMenuButton asChild isActive={route().current(item.active)}>
                    <Link href={item.url}>
                      <HugeiconsIcon icon={item.icon} size={24} />
                      <span className="font-medium">{item.label}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* Administration Group */}
        <SidebarGroup>
          <SidebarGroupLabel>الإدارة</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {administrationItems.map((item) => (
                <SidebarMenuItem key={item.label}>
                  <SidebarMenuButton asChild isActive={route().current(item.active)}>
                    <Link href={item.url}>
                      <HugeiconsIcon icon={item.icon} size={24} />
                      <span className="font-medium">{item.label}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* Help Group */}
        <SidebarGroup>
          <SidebarGroupLabel>المساعدة</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {helpItems.map((item) => (
                <SidebarMenuItem key={item.label}>
                  <SidebarMenuButton asChild isActive={route().current(item.active)}>
                    <Link href={item.url}>
                      <HugeiconsIcon icon={item.icon} size={24} />
                      <span className="font-medium">{item.label}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter>
        <div className="flex justify-between">
          <div className="flex items-center space-x-2 text-sm">
            <HugeiconsIcon icon={User02Icon} size={20} />
            {sidebar.open ? <span className="mt-1">{user.name}</span> : null}
          </div>
          {sidebar.open ? (
            <Button
              size="xs"
              variant="ghost"
              className="text-red-500 hover:text-red-700"
              onClick={() => {
                router.post(route('logout'));
                queryClient.clear();
              }}
            >
              تسجيل خروج
            </Button>
          ) : null}
        </div>
      </SidebarFooter>
    </Sidebar>
  );
});
export { AppSidebar };

// Tree Management Items
const treeManagementItems = [
  {
    label: 'إدارة الشجرة',
    url: route('branches.show'),
    active: 'branches.show',
    icon: TreeIcon,
  },
  {
    label: 'توزيع الشجرة',
    url: route('distribution.index'),
    active: 'distribution.index',
    icon: Chart03Icon,
  },
  {
    label: 'قائمة الأفراد',
    url: route('node-list.index'),
    active: 'node-list.*',
    icon: TableIcon,
  },
];

// Family Content Items
const familyContentItems = [
  {
    label: 'أخبار ومناسبات العائلة',
    url: route('posts.index'),
    active: 'posts.index',
    icon: CalendarIcon,
  },
];

// Requests Items
const requestsItems = [
  {
    label: 'طلبات الإضافة',
    url: route('node-additions.index'),
    active: 'node-additions.*',
    icon: MailAdd01Icon,
  },
  {
    label: 'طلبات التعديل',
    url: route('node-changes.index'),
    active: 'node-changes.*',
    icon: MailEdit01Icon,
  },
];

// Administration Items
const administrationItems = [
  {
    label: 'الإحصائيات',
    url: route('statistics.index'),
    active: 'statistics.*',
    icon: Chart01Icon,
  },
  {
    label: 'المستخدمون',
    url: route('users.index'),
    active: 'users.*',
    icon: UserGroup02Icon,
  },
  {
    label: 'إعدادات العائلة',
    url: route('settings.general.index'),
    active: 'settings.general.*',
    icon: SettingsIcon,
  },
  {
    label: 'إعدادات التطبيق',
    url: route('settings.app.index'),
    active: 'settings.app.*',
    icon: SettingsIcon,
  },
];

// Help Items
const helpItems = [
  {
    label: 'دليل الاستخدام',
    url: route('onboarding'),
    active: 'onboarding',
    icon: HelpCircleIcon,
  },
];
