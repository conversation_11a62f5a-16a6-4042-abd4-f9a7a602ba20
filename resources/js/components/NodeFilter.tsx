import { BallB<PERSON>Spinner } from '@/components/loaders/BallBeatSpinner';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { NodeModel } from '@/types/models';
import { MultiplicationSignIcon } from '@hugeicons-pro/core-stroke-standard';
import { HugeiconsIcon } from '@hugeicons/react';
import { useQuery } from '@tanstack/react-query';
import axios from 'axios';
import { ReactNode, useMemo } from 'react';
import { useLocalStorage } from 'react-use';
import { useDebounce } from 'use-debounce';

const fetchNodes = async (url: string, text: string, urlParams: Record<string, unknown>): Promise<NodeModel[]> => {
  const response = await axios({
    url: url,
    method: 'GET',
    params: {
      text: text.replace(/بن|بنت/g, ''),
      ...urlParams,
    },
  });

  return response.data.data.nodes;
};

type Props = {
  url?: string;
  urlParams?: Record<string, unknown>;
  resetAfterSelect?: boolean;
  disabled?: boolean;
  selected?: NodeModel | null;
  onSelect: (node: NodeModel | null) => void;
  children?: ReactNode;
};

export function NodeFilter({
  url = route('nodes.filter.index'),
  urlParams = {},
  resetAfterSelect = false,
  disabled = false,
  selected,
  onSelect,
  children,
}: Props) {
  const [filterText = '', setFilterText] = useLocalStorage('node-filter-text', '');
  const [debouncedFilterText] = useDebounce(filterText, 350);

  const { data: filteredNodes = [], isFetching } = useQuery({
    queryKey: ['nodes', 'filter', debouncedFilterText, url, urlParams],
    queryFn: () => fetchNodes(url, debouncedFilterText, urlParams),
    enabled: !!debouncedFilterText.trim(),
    placeholderData: (previousData) => previousData,
  });

  // Include selected node in the list if it's not already there
  const nodesToShow = useMemo(() => {
    if (!selected) return filteredNodes;

    const isSelectedInFiltered = filteredNodes.some((node) => node.id === selected.id);
    if (isSelectedInFiltered) return filteredNodes;

    return [selected, ...filteredNodes];
  }, [filteredNodes, selected]);

  const handleSelect = (node: NodeModel) => {
    onSelect(node);

    if (resetAfterSelect) {
      setFilterText('');
    }
  };

  return (
    <div className="mt-4 flex flex-col">
      <div className="flex flex-wrap gap-4">
        <Input
          value={filterText}
          onChange={(e) => setFilterText(e.target.value)}
          disabled={disabled}
          placeholder="بحث..."
          helper="بحث بالاسم الكامل أو رقم الجوال أو الرقم التسلسلي"
          suffix={
            selected ? (
              <Button variant="destructive" onClick={() => onSelect(null)} className="h-6 text-[9px]" size="xs">
                <HugeiconsIcon icon={MultiplicationSignIcon} size={10} color="currentColor" strokeWidth={1.5} />
                <span>إلغاء الاختيار</span>
              </Button>
            ) : null
          }
        />

        {children}
      </div>

      {(isFetching || nodesToShow.length > 0) && (
        <div className="flex flex-col">
          {isFetching && (
            <div className="mx-auto my-2">
              <BallBeatSpinner color="#10B981" />
            </div>
          )}
          {nodesToShow.length > 0 && (
            <div className="flex flex-col flex-wrap">
              <div className="w-full">
                {nodesToShow.map((node) => (
                  <Button
                    key={node.id}
                    variant={selected?.id === node.id ? 'default' : 'link'}
                    type="button"
                    onClick={() => handleSelect(node)}
                  >
                    {node.full_name}
                  </Button>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default NodeFilter;
