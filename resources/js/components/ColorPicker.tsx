import { clsx } from 'clsx';
import { useEffect, useState } from 'react';

type ColorPickerProps = {
  value?: string;
  onSelect: (color: string) => void;
};

export function ColorPicker({ value = '', onSelect }: ColorPickerProps) {
  const [selectedColor, setSelectedColor] = useState<string>(value);

  const colors = [
    '#dc2626', // Red shades
    '#ca8a04', // Orange/Yellow
    '#65a30d', // Green shades
    '#2563eb', // Blue shades
    '#7c3aed', // Purple shades
    '#9ca3af', // Soft gray
  ];

  useEffect(() => {
    setSelectedColor(value);
  }, [value]);

  const handleColorSelect = (color: string) => {
    setSelectedColor(color);
    onSelect(color);
  };

  return (
    <div className="flex gap-x-1">
      {colors.map((color) => (
        <div key={color}>
          <div
            className={clsx('flex size-3.5 cursor-pointer items-center justify-center rounded')}
            style={{ backgroundColor: color }}
            onClick={() => handleColorSelect(color)}
          >
            {selectedColor === color && <div className="size-1.5 rounded-full bg-white"></div>}
          </div>
        </div>
      ))}
    </div>
  );
}

export default ColorPicker;
