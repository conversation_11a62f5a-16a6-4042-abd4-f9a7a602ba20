import Logo from '@/components/Logo';
import Card from '@/components/ui/Card';
import { ReactNode } from 'react';

type AuthenticationCardProps = {
  typeName: string;
  children: ReactNode;
};

export default function AuthenticationCard({ typeName, children }: AuthenticationCardProps) {
  return (
    <div className="flex flex-col items-center px-2 sm:justify-center">
      <div className="mt-12">
        <Logo width={150} height={150} className="text-green-600" />
        <div className="mt-4 flex justify-center text-lg font-semibold">{typeName}</div>
      </div>

      <Card containerClassName="mt-4 w-full sm:max-w-sm">{children}</Card>
    </div>
  );
}
