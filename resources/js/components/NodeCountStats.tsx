import { StatsRow } from '@/Pages/Tenant/Statistic/StatsRow';
import { useTenant } from '@/hooks/useUser';
import React from 'react';

interface NodeCountStatsProps {
  aliveNodeNumber: number;
  deadNodeNumber: number;
  aliveMaleNumber: number;
  deadMaleNumber: number;
  aliveFemaleNumber: number;
  deadFemaleNumber: number;
  showGender?: boolean;
  showLifeStatus?: boolean;
}

export const NodeCountStats: React.FC<NodeCountStatsProps> = ({
  aliveNodeNumber,
  deadNodeNumber,
  aliveMaleNumber,
  deadMaleNumber,
  aliveFemaleNumber,
  deadFemaleNumber,
  showGender = true,
  showLifeStatus = true,
}) => {
  const tenant = useTenant();

  return (
    <div>
      <ul className="flex flex-col space-y-4">
        <StatsRow label="عدد الأفراد" value={tenant?.all_nodes_count ?? 0} />
        {showLifeStatus && (
          <>
            <StatsRow label="عدد الأحياء" value={aliveNodeNumber} nested={true} />
            <StatsRow label="عدد الأموات" value={deadNodeNumber} nested={true} />
          </>
        )}
        {showGender && (
          <>
            <StatsRow label="عدد الذكور" value={aliveMaleNumber + deadMaleNumber} />
            {showLifeStatus && (
              <>
                <StatsRow label="عدد الأحياء" value={aliveMaleNumber} nested={true} />
                <StatsRow label="عدد الأموات" value={deadMaleNumber} nested={true} />
              </>
            )}
            <StatsRow label="عدد الإناث" value={aliveFemaleNumber + deadFemaleNumber} />
            {showLifeStatus && (
              <>
                <StatsRow label="عدد الأحياء" value={aliveFemaleNumber} nested={true} />
                <StatsRow label="عدد الأموات" value={deadFemaleNumber} nested={true} />
              </>
            )}
          </>
        )}
      </ul>
    </div>
  );
};

export default NodeCountStats;
