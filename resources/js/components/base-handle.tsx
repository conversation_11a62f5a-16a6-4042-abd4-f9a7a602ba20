import { Handle, HandleProps } from '@xyflow/react';
import { forwardRef } from 'react';

import { cn } from '@/lib/utils';

export type BaseHandleProps = HandleProps;

export const BaseHandle = forwardRef<HTMLDivElement, BaseHandleProps>(({ className, children, ...props }, ref) => {
  return (
    <Handle
      ref={ref}
      {...props}
      className={cn('rounded-full border-transparent !bg-transparent transition', className)}
      {...props}
    >
      {children}
    </Handle>
  );
});

BaseHandle.displayName = 'BaseHandle';
