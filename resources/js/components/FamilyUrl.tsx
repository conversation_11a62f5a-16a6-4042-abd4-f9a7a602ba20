import { useTenant } from '@/hooks/useUser';
import clsx from 'clsx';

type Props = {
  url?: string;
  fullUrl?: string;
  className?: string;
};

export default function FamilyUrl({ fullUrl, className }: Props) {
  const tenant = useTenant()!;
  const finalFullUrl = fullUrl ?? tenant.tree_url;

  return (
    <a
      href={finalFullUrl}
      target="_blank"
      rel="noopener noreferrer"
      className={clsx('mt-2 rounded-lg text-base whitespace-nowrap text-green-600 underline', className)}
    >
      {finalFullUrl}
    </a>
  );
}
