import AppModal from '@/components/AppModal';
import PaymentForm from '@/components/PaymentForm';
import { Button } from '@/components/ui/button';
import { SparklesIcon } from '@hugeicons-pro/core-stroke-standard';
import { HugeiconsIcon } from '@hugeicons/react';
import { usePage } from '@inertiajs/react';
import posthog from 'posthog-js';
import { useState } from 'react';

type Package = {
  id: string;
  nodes: number;
  nodesFormatted: string;
  price: number;
  priceFormatted: string;
  pricePerNode: string;
  savings?: number;
  savingsFormatted?: string;
  isDiscounted?: boolean;
  originalPrice?: number;
  originalPriceFormatted?: string;
  discountPercentage?: number;
};

export default function MaxNodesDiscountModal() {
  const [showPaymentModal, setShowPaymentModal] = useState(false);

  const onBuy = () => {
    posthog.capture('Discount Offer: Accepted', {
      nodes: discountPackage.nodes,
      price: discountPackage.price,
    });
    setShowPaymentModal(true);
  };

  return (
    <div className="p-8">
      <h3 className="mb-6 text-2xl font-bold text-green-700">
        فرصة ذهبية! <HugeiconsIcon icon={SparklesIcon} className="inline-block" />
      </h3>
      <div className="mb-4 rounded-2xl bg-green-50 p-8 text-green-800 shadow-sm">
        <p className="mb-4 text-lg leading-relaxed">
          وصلت لأقصى عدد من الأفراد في شجرتك العائلية!
          <br />
          عشان ما نوقف رحلتك، جهزنا لك عرض خاص 💚
        </p>

        <div className="my-8 rounded-xl border bg-white p-6">
          <div className="flex items-center justify-center gap-4 text-lg">
            <span className="text-gray-500 line-through opacity-75">{discountPackage.originalPrice} ريال</span>
            <span className="font-bold text-green-600">{discountPackage.price} ريال</span>
            <span className="rounded-full bg-green-100 px-4 py-1.5 text-sm font-bold text-green-700">خصم 15%</span>
          </div>
          <p className="mt-3 text-center text-gray-600">تقدر تضيف 50 فرد جديد لشجرتك العائلية 🌳</p>
        </div>

        <div className="mt-8 flex justify-center">
          <Button onClick={onBuy}>احصل على ٥٠ فرد جديد</Button>
        </div>
      </div>

      <AppModal open={showPaymentModal} onOpenChange={setShowPaymentModal}>
        <PaymentForm pkg={discountPackage} />
      </AppModal>
    </div>
  );
}
