import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import BulkNodesForm from '@/Pages/Tenant/Branch/Partials/BulkNodesForm';
import UpdateNodeForm from '@/Pages/Tenant/Branch/Partials/UpdateNodeForm';
import { useNodeModalStore } from '@/store/node-modal';

import AppModal from '@/components/AppModal';
import { NodeModel } from '@/types/models';
import { PencilEdit01Icon, PlusSignIcon, UserIcon } from '@hugeicons-pro/core-stroke-standard';
import { HugeiconsIcon } from '@hugeicons/react';
import { useQuery } from '@tanstack/react-query';
import axios from 'axios';
import NodeInfo, { NodeBasicInfo } from './NodeInfo';

export default function NodeCard() {
  const nodeId = useNodeModalStore((s) => s.selectedNode?.id);

  const { data: node, isPending } = useQuery<NodeModel>({
    queryKey: ['nodes', nodeId],
    queryFn: () => axios.get(route('nodes.show', nodeId)).then((res) => res.data.data),
    enabled: Boolean(nodeId),
  });

  console.log(node);
  return (
    <div className="px-4">
      <Tabs defaultValue="info" className="w-full">
        <TabsList className="sticky top-0 z-10 grid w-full grid-cols-3 gap-x-2 bg-white">
          <TabsTrigger value="info" className="flex gap-1 text-xs">
            <HugeiconsIcon icon={UserIcon} size={16} color="currentColor" strokeWidth={1.5} />
            <span>المعلومات</span>
          </TabsTrigger>
          <TabsTrigger
            value="create"
            className="flex gap-1 bg-green-50/50 text-xs outline outline-green-100 hover:!bg-green-50 data-[state=active]:!bg-green-100 data-[state=active]:!outline-green-500"
          >
            <HugeiconsIcon icon={PlusSignIcon} size={16} color="currentColor" strokeWidth={1.5} />
            <span>إضافة فرد</span>
          </TabsTrigger>
          <TabsTrigger
            value="update"
            className="flex gap-1 bg-yellow-50/50 text-xs outline outline-yellow-100 hover:!bg-yellow-50 data-[state=active]:!bg-yellow-100 data-[state=active]:!outline-yellow-500"
          >
            <HugeiconsIcon icon={PencilEdit01Icon} size={16} color="currentColor" strokeWidth={1.5} />
            <span>تعديل</span>
          </TabsTrigger>
        </TabsList>
        <div className="sticky top-12 z-50 space-y-4 rounded-lg border border-gray-200/50 bg-gray-300/25 p-2 backdrop-blur-md">
          <NodeBasicInfo node={node} />
        </div>

        <TabsContent value="info">{node && <NodeInfo node={node} isPending={isPending} />}</TabsContent>

        <TabsContent value="create">{node && <BulkNodesForm />}</TabsContent>

        <TabsContent value="update">{node && <UpdateNodeForm parent={node.parent} selectedNode={node} />}</TabsContent>
      </Tabs>
    </div>
  );
}

export const NodeCardModal = () => {
  const activeModal = useNodeModalStore((s) => s.activeModal);
  const setNodeModal = useNodeModalStore((s) => s.setNodeModal);

  return (
    <AppModal open={activeModal === 'NODE_INFO'} onOpenChange={() => setNodeModal('NONE')}>
      <NodeCard />
    </AppModal>
  );
};
