import { Button } from '@/components/ui/button';
import Card from '@/components/ui/Card';
import { Skeleton } from '@/components/ui/skeleton';
import { Zoom } from '@/components/ui/zoom';
import NodeRelationships from '@/Shared/NodeRelationships';
import NodeRelatives from '@/Shared/NodeRelatives';
import { useNodeModalStore } from '@/store/node-modal';
import { NodeModel } from '@/types/models';
import { formatFirstThreeNames, formatHijriDate, formatLocation, otherParentInfo, visitNode } from '@/utils/helpers';
import { Female02Icon, Male02Icon, UserIcon } from '@hugeicons-pro/core-stroke-standard';
import { HugeiconsIcon } from '@hugeicons/react';
import { useMemo } from 'react';

interface NodeInfoProps {
  node: NodeModel;
  isPending: boolean;
}

export default function NodeInfo({ node, isPending }: NodeInfoProps) {
  const locationFormatted = useMemo(() => (node ? formatLocation(node) : null), [node]);

  if (isPending) {
    return (
      <div className="mt-4 flex min-h-48 items-center justify-center">
        <Skeleton className="h-20 w-20 rounded-full" />
      </div>
    );
  }

  if (!node) {
    return <div className="text-center text-gray-500">لا يوجد بيانات</div>;
  }

  return (
    <div className="mt-4 space-y-4">
      <div className="grid grid-cols-1 justify-between gap-2 sm:grid-cols-3">
        <div className="col-span-1 flex min-w-20 flex-col items-center justify-center">
          {node.photo_url ? (
            <Zoom>
              <img alt={node.full_name} className="size-20 rounded-full" src={node.photo_url} />
            </Zoom>
          ) : (
            <span className="flex size-20 items-center justify-center rounded-full border bg-gray-100">
              <HugeiconsIcon icon={UserIcon} size={40} className="text-gray-500" />
            </span>
          )}
        </div>
        <Card title="البيانات الشخصية" containerClassName="col-span-2" className="space-y-1.5">
          <InfoRow label="الجوال" value={node.mobile ?? ''} />
          <InfoRow label="البريد الإلكتروني" value={node.email ?? ''} />
          <InfoRow label="الموقع" value={locationFormatted ?? ''} />
          <InfoRow label="اللقب" value={node.nickname ?? ''} />
          <InfoRow label="الفرع" value={node.label ?? ''} />
        </Card>
      </div>

      <Card title="النبذة" containerClassName="col-span-2" className="space-y-1.5">
        {node.about ? (
          <div className="break-words whitespace-pre-wrap">{node.about}</div>
        ) : (
          <span className="font-semibold break-words whitespace-pre-wrap text-gray-300">لا يوجد</span>
        )}
      </Card>
      <NodeRelatives node={node} />
      <NodeRelationships node={node} />
    </div>
  );
}

export const NodeBasicInfo = ({ node }: { node: NodeModel | null | undefined }) => {
  const depth = useMemo(() => node?.full_name?.split(/بن|بنت/g).length, [node]);
  const otherParentName = useMemo(
    () => (node?.other_parent_relationship ? otherParentInfo(node.other_parent_relationship) : null),
    [node],
  );
  const hideNodeModal = useNodeModalStore((s) => s.hideNodeModal);

  if (!node) {
    return null;
  }

  return (
    <div>
      <div className="flex justify-between">
        <div className="flex flex-col">
          <div className="flex flex-col items-start gap-y-1">
            <div className="flex items-center gap-x-1.5">
              {node.gender === 'male' ? (
                <HugeiconsIcon size={14} icon={Male02Icon} className="text-blue-500" />
              ) : (
                <HugeiconsIcon size={14} icon={Female02Icon} className="text-pink-500" />
              )}

              <div
                style={{
                  background: node.bg_color,
                  borderRadius: '50%',
                  height: '14px',
                  width: '14px',
                }}
              ></div>
              <div className="text-sm font-bold text-gray-900">{formatFirstThreeNames(node)}</div>
            </div>
            {node.life_status === 'unknown' ? null : (
              <span
                className={
                  'inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset ' +
                  {
                    alive: 'bg-green-50 text-green-700 ring-green-600/20',
                    dead: 'bg-red-50 text-red-700 ring-red-600/20',
                  }[node.life_status]
                }
              >
                {node.life_status_ar}
              </span>
            )}
          </div>

          {otherParentName && (
            <div className="mt-1 flex items-start gap-x-2">
              <span className="text-xs text-gray-500">{node.parent?.gender == 'female' ? 'الأم' : 'الأب'}</span>
              <span className="text-xs">{otherParentName}</span>
            </div>
          )}
        </div>
        <div className="text-xs text-gray-500">
          <div className="flex text-xs font-medium">الرقم التسلسلي: {node.id}</div>
          <div className="flex items-center gap-1">
            المستوى <strong>{depth}</strong>
          </div>
        </div>
      </div>
      <div className="mt-1.5 flex justify-between">
        <div />
        <div className="flex flex-col">
          <div className="mb-2 flex gap-x-2 self-start"></div>
          {node.birth_date && (
            <span className="text-xs text-gray-500">
              ولد في <strong>{formatHijriDate(node.birth_date)}</strong>
            </span>
          )}
          {node.death_date && (
            <span className="text-xs text-gray-500">
              توفي في <strong>{formatHijriDate(node.death_date)}</strong>
            </span>
          )}
        </div>
      </div>
      <Button
        className="flex justify-self-end"
        size="xs"
        variant="outline"
        onClick={() => {
          hideNodeModal();
          visitNode(node!.id);
        }}
      >
        عرض في الشجرة
      </Button>
    </div>
  );
};

function InfoRow({ label, value }: { label: string; value: string }) {
  return (
    <div className="flex w-full flex-none justify-between gap-x-2 text-sm">
      <dt className="font-medium text-gray-500">{label}</dt>
      {value ? (
        <dd className="break-words whitespace-pre-wrap text-gray-700">{value}</dd>
      ) : (
        <dd className="font-semibold break-words whitespace-pre-wrap text-gray-300">لا يوجد</dd>
      )}
    </div>
  );
}
