import Card from '@/components/ui/Card';
import { Button } from '@/components/ui/button';
import { NodeModel } from '@/types/models';
import { visitNode } from '@/utils/helpers';
import { useMemo } from 'react';

type NodeRelativesProps = {
  node?: NodeModel;
};

export function NodeRelatives({ node }: NodeRelativesProps) {
  const sons = useMemo(() => node?.children?.filter((n) => n.gender === 'male') || [], [node?.children]);

  const daughters = useMemo(() => node?.children?.filter((n) => n.gender === 'female') || [], [node?.children]);

  const maleSiblings = useMemo(() => node?.siblings?.filter((n) => n.gender === 'male') || [], [node?.siblings]);

  const femaleSiblings = useMemo(() => node?.siblings?.filter((n) => n.gender === 'female') || [], [node?.siblings]);

  const hasRelatives = useMemo(
    () => sons.length > 0 || daughters.length > 0 || maleSiblings.length > 0 || femaleSiblings.length > 0,
    [sons, daughters, maleSiblings, femaleSiblings],
  );

  if (!node) {
    return null;
  }

  return (
    <Card
      title="الأقارب"
      topEnd={
        hasRelatives && (
          <div className="-mb-6 flex justify-end gap-2 text-xs text-gray-500">
            <div className="space-x-1 space-x-reverse">
              <span> الأبناء والبنات </span>
              <strong>{(sons?.length || 0) + (daughters?.length || 0)}</strong>
            </div>
            <div className="space-x-1 space-x-reverse">
              <span> الإخوان والأخوات </span>
              <strong>{(maleSiblings?.length || 0) + (femaleSiblings?.length || 0)}</strong>
            </div>
          </div>
        )
      }
    >
      {hasRelatives && (
        <div className="mt-10 flex gap-4">
          {maleSiblings.length > 0 && (
            <Card title="الإخوان">
              <div className="flex flex-wrap gap-2">
                {maleSiblings.map((sibling) => (
                  <Button key={sibling.id} size="sm" onClick={() => visitNode(sibling.id)}>
                    {sibling.name}
                  </Button>
                ))}
              </div>
            </Card>
          )}

          {femaleSiblings.length > 0 && (
            <Card title="الأخوات">
              <div className="flex flex-wrap gap-2">
                {femaleSiblings.map((sibling) => (
                  <Button key={sibling.id} size="sm" onClick={() => visitNode(sibling.id)}>
                    {sibling.name}
                  </Button>
                ))}
              </div>
            </Card>
          )}

          {sons.length > 0 && (
            <Card title="الأبناء">
              <div className="flex flex-wrap gap-2">
                {sons.map((son) => (
                  <Button key={son.id} size="sm" onClick={() => visitNode(son.id)}>
                    {son.name}
                  </Button>
                ))}
              </div>
            </Card>
          )}

          {daughters.length > 0 && (
            <Card title="البنات">
              <div className="flex flex-wrap gap-2">
                {daughters.map((daughter) => (
                  <Button key={daughter.id} size="sm" onClick={() => visitNode(daughter.id)}>
                    {daughter.name}
                  </Button>
                ))}
              </div>
            </Card>
          )}
        </div>
      )}
    </Card>
  );
}

export default NodeRelatives;
