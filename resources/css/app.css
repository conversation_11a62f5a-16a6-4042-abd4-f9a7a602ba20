@import 'tailwindcss';
@import './moyasar.css';

@plugin 'tailwindcss-animate';

@source '../views';

@theme {
  --font-sans: 'LamaSans', sans-serif;

  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);

  --color-background: var(--background);
  --color-foreground: var(--foreground);

  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);

  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);

  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);

  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);

  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);

  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);

  --color-destructive: var(--destructive);

  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);

  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);

  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  /* redefine gray to stone*/
  --color-gray-50: oklch(0.985 0.003 45);
  --color-gray-100: oklch(0.97 0.003 45);
  --color-gray-200: oklch(0.923 0.008 38);
  --color-gray-300: oklch(0.869 0.012 40);
  --color-gray-400: oklch(0.709 0.025 42);
  --color-gray-500: oklch(0.553 0.035 44);
  --color-gray-600: oklch(0.444 0.03 46);
  --color-gray-700: oklch(0.374 0.025 48);
  --color-gray-800: oklch(0.268 0.018 35);
  --color-gray-900: oklch(0.216 0.015 38);
  --color-gray-950: oklch(0.147 0.012 40);
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-100, currentColor);
  }
}

:root {
  --radius: 0.5rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.141 0.005 285.823);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.141 0.005 285.823);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.141 0.005 285.823);
  --primary: oklch(0.723 0.219 149.579);
  --primary-foreground: oklch(0.982 0.018 155.826);
  --secondary: oklch(0.967 0.001 286.375);
  --secondary-foreground: oklch(0.21 0.006 285.885);
  --muted: oklch(0.967 0.001 286.375);
  --muted-foreground: oklch(0.552 0.016 285.938);
  --accent: oklch(0.967 0.001 286.375);
  --accent-foreground: oklch(0.21 0.006 285.885);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.92 0.004 286.32);
  --input: oklch(0.92 0.004 286.32);
  --ring: oklch(0.723 0.219 149.579);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.141 0.005 285.823);
  --sidebar-primary: oklch(0.723 0.219 149.579);
  --sidebar-primary-foreground: oklch(0.982 0.018 155.826);
  --sidebar-accent: oklch(0.967 0.001 286.375);
  --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
  --sidebar-border: oklch(0.92 0.004 286.32);
  --sidebar-ring: oklch(0.723 0.219 149.579);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }

  html {
    font-size: 14.5px;
  }
}

@font-face {
  font-family: LamaSans;
  font-weight: 400;
  font-style: normal;
  src:
    url('@/assets/fonts/LamaSans-Regular.woff2') format('woff2'),
    url('@/assets/fonts/LamaSans-Regular.ttf') format('truetype');
  font-display: swap;
}

@font-face {
  font-family: LamaSans;
  font-weight: 500;
  font-style: normal;
  src:
    url('@/assets/fonts/LamaSans-Medium.woff2') format('woff2'),
    url('@/assets/fonts/LamaSans-Medium.ttf') format('truetype');
  font-display: swap;
}

@font-face {
  font-family: LamaSans;
  font-weight: 600;
  font-style: normal;
  src:
    url('@/assets/fonts/LamaSans-SemiBold.woff2') format('woff2'),
    url('@/assets/fonts/LamaSans-SemiBold.ttf') format('truetype');
  font-display: swap;
}

@font-face {
  font-family: LamaSans;
  font-weight: 700;
  font-style: normal;
  src:
    url('@/assets/fonts/LamaSans-Bold.woff2') format('woff2'),
    url('@/assets/fonts/LamaSans-Bold.ttf') format('truetype');
  font-display: swap;
}

body {
  font-family: LamaSans, Roboto, serif;
}

@layer utilities {
  .text-gradient {
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

.svg-bg {
  background-repeat: no-repeat;
  background-size: cover;
  backdrop-filter: brightness(0.5);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' version='1.1' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:svgjs='http://svgjs.com/svgjs' width='1440' height='627' preserveAspectRatio='none' viewBox='0 0 1440 627'%3e%3cg mask='url(%26quot%3b%23SvgjsMask1029%26quot%3b)' fill='none'%3e%3crect width='1440' height='627' x='0' y='0' fill='rgba(240%2c 249%2c 255%2c 1)'%3e%3c/rect%3e%3cpath d='M1488 627L0 627 L0 278.11Q84.86 242.97%2c 120 327.83Q148.54 284.37%2c 192 312.91Q242.69 243.6%2c 312 294.28Q366.55 228.82%2c 432 283.37Q479.13 258.5%2c 504 305.64Q574.57 256.21%2c 624 326.78Q654.78 285.56%2c 696 316.34Q726.31 274.65%2c 768 304.96Q788.1 253.06%2c 840 273.16Q882.17 243.34%2c 912 285.51Q973.25 274.75%2c 984 336Q1005.71 285.71%2c 1056 307.41Q1084.33 263.74%2c 1128 292.07Q1209.42 253.5%2c 1248 334.92Q1298.26 265.18%2c 1368 315.43Q1404.26 231.69%2c 1488 267.95z' fill='rgba(224%2c 242%2c 254%2c 1)'%3e%3c/path%3e%3cpath d='M1488 627L0 627 L0 320.49Q47.03 295.52%2c 72 342.56Q114.9 313.46%2c 144 356.35Q210.7 303.05%2c 264 369.75Q285.95 319.7%2c 336 341.65Q378.45 312.1%2c 408 354.55Q479.47 306.02%2c 528 377.5Q535.37 312.87%2c 600 320.23Q667.7 267.94%2c 720 335.64Q772.29 315.93%2c 792 368.22Q826.22 282.44%2c 912 316.66Q962.31 294.98%2c 984 345.29Q1028.58 317.87%2c 1056 362.45Q1124.05 310.5%2c 1176 378.55Q1210.94 293.49%2c 1296 328.43Q1337.61 298.03%2c 1368 339.64Q1434.73 286.37%2c 1488 353.1z' fill='rgba(219%2c 234%2c 254%2c 1)'%3e%3c/path%3e%3cpath d='M1560 627L0 627 L0 367.9Q57.69 353.59%2c 72 411.28Q115.41 334.69%2c 192 378.1Q276.05 342.15%2c 312 426.21Q356.16 350.37%2c 432 394.52Q489.75 380.27%2c 504 438.03Q505.36 367.39%2c 576 368.75Q633.54 354.29%2c 648 411.83Q668.4 360.24%2c 720 380.64Q763.08 351.72%2c 792 394.8Q849.79 380.59%2c 864 438.37Q916.34 370.71%2c 984 423.04Q994.93 361.97%2c 1056 372.9Q1101.12 346.02%2c 1128 391.14Q1212.12 355.25%2c 1248 439.37Q1249.57 368.94%2c 1320 370.5Q1386.28 316.78%2c 1440 383.07Q1519.24 342.31%2c 1560 421.55z' fill='rgba(229%2c 247%2c 255%2c 0.62)'%3e%3c/path%3e%3cpath d='M1488 627L0 627 L0 435.21Q71.87 387.08%2c 120 458.95Q149.36 416.31%2c 192 445.67Q221.88 403.56%2c 264 433.44Q312 409.44%2c 336 457.45Q407.05 408.5%2c 456 479.54Q494.18 397.72%2c 576 435.89Q663.14 403.03%2c 696 490.18Q710.51 432.69%2c 768 447.21Q810.74 417.94%2c 840 460.68Q860.25 408.93%2c 912 429.17Q997.97 395.14%2c 1032 481.11Q1047.76 424.87%2c 1104 440.63Q1178.83 395.46%2c 1224 470.29Q1266.74 441.03%2c 1296 483.78Q1318.35 434.13%2c 1368 456.48Q1414.65 383.13%2c 1488 429.79z' fill='rgba(236%2c 254%2c 255%2c 1)'%3e%3c/path%3e%3cpath d='M1512 627L0 627 L0 507.14Q58.9 446.04%2c 120 504.94Q171.27 436.21%2c 240 487.48Q293.2 420.69%2c 360 473.89Q436.89 430.78%2c 480 507.67Q531.15 486.82%2c 552 537.97Q581.17 495.14%2c 624 524.32Q640.71 469.04%2c 696 485.75Q745.63 463.38%2c 768 513Q838.6 463.6%2c 888 534.19Q921.32 447.51%2c 1008 480.84Q1056.53 457.37%2c 1080 505.89Q1146.43 452.33%2c 1200 518.76Q1265.92 464.68%2c 1320 530.6Q1336.29 474.89%2c 1392 491.18Q1444.33 423.5%2c 1512 475.83z' fill='rgba(224%2c 242%2c 254%2c 1)'%3e%3c/path%3e%3cpath d='M1464 627L0 627 L0 550.51Q74.8 505.31%2c 120 580.11Q157.27 497.38%2c 240 534.65Q327.94 502.59%2c 360 590.52Q408.33 518.85%2c 480 567.18Q518.16 485.34%2c 600 523.5Q694.37 497.87%2c 720 592.24Q728.9 529.14%2c 792 538.05Q865.74 491.78%2c 912 565.52Q987.11 520.63%2c 1032 595.73Q1061.38 505.11%2c 1152 534.49Q1234.6 497.09%2c 1272 579.68Q1291.29 526.97%2c 1344 546.26Q1394.97 477.23%2c 1464 528.19z' fill='rgba(240%2c 249%2c 255%2c 1)'%3e%3c/path%3e%3cpath d='M1488 627L0 627 L0 623.61Q15.06 566.67%2c 72 581.73Q116.91 554.64%2c 144 599.55Q192.25 575.8%2c 216 624.05Q253.29 541.34%2c 336 578.62Q385.9 556.52%2c 408 606.42Q455.39 581.81%2c 480 629.2Q509.85 587.05%2c 552 616.89Q605.01 549.9%2c 672 602.91Q729.95 588.86%2c 744 646.81Q775.05 557.86%2c 864 588.91Q937.53 542.44%2c 984 615.97Q1026.73 586.7%2c 1056 629.43Q1092.72 546.15%2c 1176 582.87Q1220.79 555.66%2c 1248 600.46Q1315.03 547.49%2c 1368 614.52Q1437.74 564.26%2c 1488 634z' fill='rgba(255%2c 255%2c 255%2c 1)'%3e%3c/path%3e%3c/g%3e%3cdefs%3e%3cmask id='SvgjsMask1029'%3e%3crect width='1440' height='627' fill='white'%3e%3c/rect%3e%3c/mask%3e%3c/defs%3e%3c/svg%3e");
}

/*
  ---break---
*/

@theme inline {
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

/* src/components/FamilyTreeMarquee.css */
.marquee-container {
  width: 100%;
  overflow: hidden;
  position: relative;
  --fade-color-start: rgba(255, 255, 255, 1);
  --fade-color-end: rgba(255, 255, 255, 0.5);
  mask-image: linear-gradient(
    to right,
    var(--fade-color-end) 0%,
    var(--fade-color-start) 15%,
    var(--fade-color-start) 85%,
    var(--fade-color-end) 100%
  );
  -webkit-mask-image: linear-gradient(
    to right,
    var(--fade-color-end) 0%,
    var(--fade-color-start) 15%,
    var(--fade-color-start) 85%,
    var(--fade-color-end) 100%
  );
}

.marquee-track {
  display: flex;
  width: max-content;
  flex-shrink: 0;
  padding-block: 0.75rem;
  will-change: transform;
  animation: linear infinite;
}

@keyframes marquee-rtl {
  from {
    transform: translateX(0%);
  }
  to {
    transform: translateX(50%);
  }
}

@keyframes marquee-reverse-rtl {
  from {
    transform: translateX(50%);
  }
  to {
    transform: translateX(0%);
  }
}

.animate-marquee-normal {
  animation-name: marquee-rtl;
  animation-duration: 150s;
}
.animate-marquee-fast-reverse {
  animation-name: marquee-reverse-rtl;
  animation-duration: 140s;
}
.animate-marquee-slow {
  animation-name: marquee-rtl;
  animation-duration: 170s;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) skewX(-12deg);
  }
  100% {
    transform: translateX(200%) skewX(-12deg);
  }
}
