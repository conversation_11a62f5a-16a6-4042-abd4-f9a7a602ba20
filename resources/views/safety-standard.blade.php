<!DOCTYPE html>
<html style='height: 100vh;' lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1 maximum-scale=1.0, user-scalable=no">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="description" content="أوراق لتصميم شجرة العائلة الإلكترونية والورقية">

    @vite(['resources/css/app.css', 'resources/js/app.tsx'])
</head>
<body class="font-sans antialiased bg-gray-100 mb-4">
<div class="flex justify-center mt-12">
    <img src="{{asset('mail/logo.png')}}" width="100">
</div>
<h1 class="text-center text-xl font-bold mt-4">معايير السلامة وحماية الأطفال</h1>
<div class="p-4 flex justify-center whitespace-pre-wrap">
    <div class="bg-white rounded shadow-sm border p-3 text-wrap">
        معايير حماية الطفل من الإساءة والاستغلال
        نحن في تطبيق أوراق نلتزم بأعلى معايير حماية الأطفال من الإساءة والاستغلال الجنسي (CSAE). نضع سلامة مستخدمينا،
        وخاصة القصّر منهم، في مقدمة أولوياتنا.

        سياسات الحماية
        - منع نشر أي محتوى يتعلق باستغلال الأطفال
        - حظر التواصل غير الآمن مع القاصرين
        - مراقبة المحتوى بشكل مستمر
        - آليات الإبلاغ الفوري عن المحتوى المسيء

        إجراءات السلامة المتبعة
        نتخذ إجراءات صارمة لضمان سلامة المستخدمين من خلال:
        - التحقق من هوية المستخدمين
        - مراجعة المحتوى قبل النشر
        - تقييد الوصول للمحتوى حسب العمر
        - نظام تقارير وبلاغات فعال

        آليات الإبلاغ والمراقبة
        - نظام تقارير سهل الاستخدام
        - فريق مراقبة متخصص يعمل على مدار الساعة
        - تعاون مباشر مع الجهات المختصة
        - استجابة فورية للبلاغات الواردة

        التزامنا تجاه المستخدمين
        نلتزم بـ:
        - حماية خصوصية المستخدمين القصّر
        - منع أي شكل من أشكال الاستغلال
        - توفير بيئة آمنة للجميع
        - التحديث المستمر لمعايير السلامة

        التحديث والمراجعة
        تتم مراجعة وتحديث هذه المعايير بشكل دوري لضمان فعاليتها واستمرار تطويرها بما يتناسب مع المتطلبات الحديثة لحماية
        المستخدمين.
    </div>
</div>
</body>
</html>
