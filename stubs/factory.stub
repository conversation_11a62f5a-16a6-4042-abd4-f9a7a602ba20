<?php

namespace {{ factoryNamespace }};

use Illuminate\Database\Eloquent\Factories\Factory;
use {{ namespacedModel }};

class {{ factory }}Factory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = {{ model }}::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            //
        ];
    }
}
