APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US
APP_MAINTENANCE_DRIVER=file
APP_MAINTENANCE_STORE=database
PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=awraq
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_CONNECTION=log
CACHE_STORE=redis
QUEUE_CONNECTION=sync
SESSION_DRIVER=redis
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=resend
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME=awraq

FILESYSTEM_DISK=s3

AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=zFx9q2yxL25Zf9LdT3Lxe0xwxdalqmyplwFl4RYQ
AWS_DEFAULT_REGION=eu-central-1
AWS_BUCKET=awraq-production

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

MSEGAT_USERNAME=kmlx28
MSEGAT_USER_SENDER=awraq
MSEGAT_API_KEY=e4cbfef58ea2111a35bc1e89fd8358ce

RESEND_API_KEY=

VITE_MOYASAR_PUBLISHABLE_KEY=your_publishable_key
MOYASAR_SECRET_KEY=your_secret_key

# Expo Push Notifications
EXPO_ACCESS_TOKEN=
