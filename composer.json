{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.2", "awobaz/compoships": "^2.4", "caneara/quest": "^v3.0.0", "chrysanthos/pulse-requests": "^1.0", "eusonlito/laravel-pulse-apps-load": "dev-master", "geniusts/hijri-dates": "^1.1", "inertiajs/inertia-laravel": "^2.0", "laravel-notification-channels/expo": "^2.1", "laravel/framework": "^12.7", "laravel/horizon": "^5.30", "laravel/octane": "^2.8", "laravel/pulse": "^1.2", "laravel/sanctum": "^4.0", "laravel/socialite": "^5.19", "laravel/tinker": "^2.10.1", "league/flysystem-aws-s3-v3": "^3.0", "posthog/posthog-php": "^3.3", "praatmetdedokter/pulse-redis-monitor": "^0.1.3", "rap2hpoutre/fast-excel": "^5.6", "resend/resend-laravel": "^0.17.0", "robertogallea/pulse-db-size": "^1.2", "sentry/sentry-laravel": "^4.13", "spatie/laravel-backup": "^9.2.9", "spatie/laravel-ray": "^1.39", "staudenmeir/laravel-adjacency-list": "^1.24", "tightenco/ziggy": "^2.0", "timacdonald/pulse-validation-errors": "^1.5"}, "require-dev": {"fakerphp/faker": "^1.23", "laravel/pail": "^1.2.2", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.6", "pestphp/pest": "^3.8"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"files": ["app/util/helpers.php"], "psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "minimum-stability": "stable", "prefer-stable": true}