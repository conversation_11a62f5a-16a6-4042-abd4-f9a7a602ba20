<?php

namespace App\Observers;

use App\Models\Node;

class NodeObserver
{
    public function created(Node $node): void
    {
        $node->tenant->updateStats();
        $node->tenant->forgetCache('node_count');
        $node->tenant->forgetCache('male_node_count');
        $node->tenant->forgetCache('female_node_count');
    }

    public function updated(Node $node): void
    {
        $node->tenant->updateStats();
    }

    public function deleted(Node $node): void
    {
        $node->tenant->updateStats();
        $node->tenant->forgetCache('node_count');
        $node->tenant->forgetCache('male_node_count');
        $node->tenant->forgetCache('female_node_count');
    }
}
