<?php

namespace App\Observers;

use App\Models\Relationship;

class RelationshipObserver
{
    public function created(Relationship $relationship): void
    {
        postHog(['event' => 'Relationship: Created']);
    }

    public function updated(Relationship $relationship): void
    {
        postHog(['event' => 'Relationship: Updated']);
    }

    public function deleted(Relationship $relationship): void
    {
        postHog(['event' => 'Relationship: Deleted']);
    }
}
