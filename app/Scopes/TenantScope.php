<?php

namespace App\Scopes;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

use function request;
use function session;

class TenantScope implements Scope
{
    public function apply(Builder $builder, Model $model)
    {
        if (request()->routeIs('admin.*')) {
            return;
        }

        $tenantId = session('tenant_id') ?? request()->user()?->tenant_id;

        if ($tenantId) {
            $builder->where('tenant_id', $tenantId);
        }
    }
}
