<?php

namespace App\Jobs;

use App\Models\Node;
use App\Services\ProcessNode;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\DB;

class UpdateNodeStyleJob implements ShouldQueue
{
    use Queueable;

    public function __construct(private int $nodeId, private array $style, private ?string $bgColor) {}

    public function handle(): void
    {
        DB::transaction(function () {
            $node = Node::find($this->nodeId);

            if (!$node) {
                return;
            }

            $node->update([
                'style' => [...$node->style ?? ProcessNode::initialNodeStyle($node->parent), ...$this->style],
                'bg_color' => $this->bgColor ?? $node->bg_color,
            ]);
        }, 5);
    }
}
