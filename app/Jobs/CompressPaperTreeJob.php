<?php

namespace App\Jobs;

use App\Models\Setting;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class CompressPaperTreeJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 3;
    public $timeout = 300; // 5 minutes

    public function __construct(
        public string $filePath
    ) {}

    public function handle(): void
    {
        if (!Storage::exists($this->filePath)) {
            Log::warning('CompressPaperTreeJob: File does not exist', ['file_path' => $this->filePath]);
            return;
        }

        // Check if it's a PDF file
        $mimeType = Storage::mimeType($this->filePath);
        if ($mimeType !== 'application/pdf') {
            Log::warning('CompressPaperTreeJob: File is not a PDF', [
                'file_path' => $this->filePath,
                'mime_type' => $mimeType
            ]);
            return;
        }

        try {
            $originalSize = Storage::size($this->filePath);
            
            $compressedContent = $this->compressWithILovePDF();
            
            if ($compressedContent) {
                // Replace the original file with compressed version
                Storage::put($this->filePath, $compressedContent);
                
                $newSize = Storage::size($this->filePath);
                
                Log::info('Paper tree PDF compression completed successfully', [
                    'file_path' => $this->filePath,
                    'original_size' => $originalSize,
                    'compressed_size' => $newSize,
                    'compression_ratio' => round((1 - $newSize / $originalSize) * 100, 2),
                ]);
            } else {
                throw new \Exception('Compression failed: No compressed content returned');
            }
        } catch (\Exception $e) {
            Log::error('Paper tree PDF compression failed', [
                'file_path' => $this->filePath,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            throw $e;
        }
    }

    private function compressWithILovePDF(): ?string
    {
        $apiKey = config('services.ilove_pdf.api_key');
        $baseUrl = 'https://api.ilovepdf.com/v1';
        
        if (!$apiKey) {
            throw new \Exception('iLovePDF API key not configured');
        }

        // Step 1: Get auth token
        $authResponse = Http::post("{$baseUrl}/auth", [
            'public_key' => $apiKey,
        ]);

        if (!$authResponse->successful()) {
            throw new \Exception('Failed to authenticate with iLovePDF: ' . $authResponse->body());
        }

        $token = $authResponse->json('token');

        // Step 2: Start compress task
        $taskResponse = Http::withToken($token)->post("{$baseUrl}/start/compress");

        if (!$taskResponse->successful()) {
            throw new \Exception('Failed to start compress task: ' . $taskResponse->body());
        }

        $taskId = $taskResponse->json('task');

        // Step 3: Upload file
        $fileContent = Storage::get($this->filePath);
        $filename = basename($this->filePath);
        
        $uploadResponse = Http::withToken($token)
            ->attach('file', $fileContent, $filename)
            ->post("{$baseUrl}/upload", [
                'task' => $taskId,
            ]);

        if (!$uploadResponse->successful()) {
            throw new \Exception('Failed to upload file: ' . $uploadResponse->body());
        }

        // Step 4: Process compression
        $processResponse = Http::withToken($token)->post("{$baseUrl}/process", [
            'task' => $taskId,
            'compression_level' => 'recommended', // Options: low, recommended, extreme
        ]);

        if (!$processResponse->successful()) {
            throw new \Exception('Failed to process compression: ' . $processResponse->body());
        }

        // Step 5: Download compressed file content
        $downloadResponse = Http::withToken($token)->get("{$baseUrl}/download/{$taskId}");

        if (!$downloadResponse->successful()) {
            throw new \Exception('Failed to download compressed file: ' . $downloadResponse->body());
        }

        // Return the compressed file content
        return $downloadResponse->body();
    }

    public function failed(\Throwable $exception): void
    {
        Log::error('CompressPaperTreeJob failed permanently', [
            'file_path' => $this->filePath,
            'error' => $exception->getMessage(),
            'total_attempts' => $this->attempts(),
        ]);
    }
}
