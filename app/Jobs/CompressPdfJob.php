<?php

namespace App\Jobs;

use App\Models\Document;
use App\Services\ILovePdfService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class CompressPdfJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 3;
    public $timeout = 300; // 5 minutes

    public function __construct(
        public Document $document
    ) {}

    public function handle(ILovePdfService $ilovePdfService): void
    {
        if (!$this->document->isPdf()) {
            Log::warning('CompressPdfJob: Document is not a PDF', ['document_id' => $this->document->id]);
            return;
        }

        try {
            $originalSize = $this->document->file_size;

            // Compress the PDF using the service
            $compressedContent = $ilovePdfService->compressPdf($this->document->file_path);

            // Replace the original file with compressed version
            Storage::put($this->document->file_path, $compressedContent);

            // Update file size
            $newSize = Storage::size($this->document->file_path);
            $this->document->update(['file_size' => $newSize]);

            Log::info('PDF compression completed successfully', [
                'document_id' => $this->document->id,
                'original_size' => $originalSize,
                'compressed_size' => $newSize,
                'compression_ratio' => round((1 - $newSize / $originalSize) * 100, 2),
            ]);

        } catch (\Exception $e) {
            Log::error('PDF compression failed', [
                'document_id' => $this->document->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }



    public function failed(\Throwable $exception): void
    {
        Log::error('CompressPdfJob failed permanently', [
            'document_id' => $this->document->id,
            'error' => $exception->getMessage(),
            'total_attempts' => $this->attempts(),
        ]);
    }
}
