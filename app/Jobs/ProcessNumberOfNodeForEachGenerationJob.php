<?php

namespace App\Jobs;

use App\Models\Tenant;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;

class ProcessNumberOfNodeForEachGenerationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(protected Tenant $tenant)
    {
        $this->onQueue('low');
    }

    public function handle(): void
    {
        if ($this->tenant->nodes()->count() === 1) {
            return;
        }

        $generationCounts = collect();

        $this->tenant
            ->nodes()
            ->withCount('ancestors')
            ->chunk(1000, function ($nodesChunk) use (&$generationCounts) {
                $counts = $this->countNodesByGeneration($nodesChunk);
                $generationCounts = $this->accumulateCounts($generationCounts, $counts);
            });

        $this->tenant->stat->update([
            'number_of_nodes_in_each_generation' => $generationCounts->pluck('count'),
        ]);
    }

    private function countNodesByGeneration($nodes)
    {
        return collect($nodes)
            ->map(
                fn($node) => [
                    'generation' => $node->ancestors_count + 1,
                    'node' => $node,
                ]
            )
            ->groupBy('generation')
            ->map(
                fn($group, $generation) => [
                    'generation' => $generation,
                    'count' => $group->count(),
                ]
            )
            ->values();
    }

    private function accumulateCounts(Collection $accumulatedCounts, $newCounts)
    {
        return $accumulatedCounts
            ->merge($newCounts)
            ->groupBy('generation')
            ->map(
                fn($group) => [
                    'generation' => $group->first()['generation'],
                    'count' => $group->sum('count'),
                ]
            )
            ->values();
    }
}
