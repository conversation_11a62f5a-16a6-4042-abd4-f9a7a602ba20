<?php

namespace App\Jobs;

use App\Models\Node;
use App\Models\Tenant;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;

class ProcessNodeMostCommonFullNamesJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(protected Tenant $tenant)
    {
        $this->onQueue('low');
    }

    public function handle(): void
    {
        if ($this->tenant->nodes()->count() === 1) {
            return;
        }

        $segmentLengths = collect([3, 4, 5, 6]);
        $accumulatedCounts = collect();

        $this->tenant->nodes()->chunk(1000, function ($nodesChunk) use ($segmentLengths, &$accumulatedCounts) {
            $segmentLengths->each(function ($length) use ($nodesChunk, &$accumulatedCounts) {
                $formattedNodes = $this->formatNodes($nodesChunk, $length);
                $counts = $this->groupByNodeNameCount($formattedNodes);
                $accumulatedCounts = $this->accumulateCounts($accumulatedCounts, $counts);
            });
        });

        $this->tenant->stat->update([
            'most_common_full_names' => $segmentLengths->map(
                fn($length) => [
                    'length' => $length,
                    'names' => $this->getMostCommonNames($accumulatedCounts, $length, 10),
                ]
            ),
        ]);
    }

    private function groupByNodeNameCount($nodes)
    {
        return collect($nodes)
            ->groupBy('name')
            ->map(
                fn($group) => [
                    'name' => $group->first()['name'],
                    // When multiple siblings have the same name, we count them as one
                    'count' => $group->unique('parent_id')->count(),
                ]
            )
            ->values();
    }

    private function formatNodes($nodes, $length)
    {
        return $nodes
            ->map(
                fn(Node $node) => [
                    'parent_id' => $node->parent_id,
                    'gender' => $node->gender->value,
                    'name' => $this->cleanFullName($node, $length),
                ]
            )
            ->filter(fn($node) => count($node['name']) === $length)
            ->map(
                fn($node) => [
                    'parent_id' => $node['parent_id'],
                    'gender' => $node['gender'],
                    'name' =>
                        "{$node['name'][0]} " .
                        ($node['gender'] === 'male' ? 'بن' : 'بنت') .
                        ' ' .
                        implode(' بن ', array_slice($node['name'], 1)),
                ]
            )
            ->values();
    }

    private function cleanFullName($node, $length)
    {
        return collect(explode('بن', str_replace('بنت', 'بن', $node->full_name)))
            ->map(fn($name) => trim($name))
            ->slice(0, $length)
            ->all();
    }

    private function accumulateCounts(Collection $accumulatedCounts, $newCounts)
    {
        return $accumulatedCounts
            ->merge($newCounts)
            ->groupBy('name')
            ->map(
                fn($group) => [
                    'name' => $group->first()['name'],
                    'count' => $group->sum('count'),
                ]
            )
            ->values();
    }

    private function getMostCommonNames($accumulatedCounts, $length, $topN)
    {
        return $accumulatedCounts
            ->filter(fn($item) => count(explode(' بن ', $item['name'])) === $length)
            ->sortByDesc('count')
            ->take($topN)
            ->filter(fn($item) => $item['count'] > 1)
            ->values()
            ->all();
    }
}
