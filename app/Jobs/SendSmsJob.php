<?php

namespace App\Jobs;

use App\Models\SMSMessage;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;

class SendSmsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(protected SMSMessage $SMSMessage)
    {
        $this->onQueue('otp');
    }

    public function handle(): void
    {
        $response = Http::retry(10, 1000)
            ->timeout(20)
            ->asForm()
            ->post('https://www.msegat.com/gw/', [
                'userName' => config('services.msegat.username'),
                'userSender' => config('services.msegat.user_sender'),
                'apiKey' => config('services.msegat.api_key'),
                'msgEncoding' => 'UTF8',
                'msg' => $this->SMSMessage->body,
                'numbers' => $this->SMSMessage->mobile,
                'By' => 'Link',
            ]);

        $success = [1, '1', 'M0000'];

        if (in_array($response->body(), $success, true)) {
            $this->SMSMessage->update(['sent_at' => now()]);
            return;
        }

        throw new Exception("Msegat Driver: message was not sent, Code={$response->body()}");
    }
}
