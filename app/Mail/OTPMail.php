<?php

namespace App\Mail;

use App\Models\OneTimePassword;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class OTPMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public function __construct(public OneTimePassword $otp)
    {
        $this->onQueue('otp');
    }

    public function envelope(): Envelope
    {
        return new Envelope(subject: 'رقم التحقق');
    }

    public function content(): Content
    {
        return new Content(markdown: 'emails.o-t-p');
    }
}
