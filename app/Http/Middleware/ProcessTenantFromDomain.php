<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ProcessTenantFromDomain
{
    public function handle(Request $request, Closure $next): Response
    {
        if ($request->url === 'www') {
            return redirect()->to(config('app.url'));
        }

        $tenant = tenant($request->url);

        abort_if(!$tenant, 404);

        $request->merge(['tenant' => $tenant]);

        $request->route()->forgetParameter('url');

        return $next($request);
    }
}
