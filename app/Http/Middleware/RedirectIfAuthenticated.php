<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class RedirectIfAuthenticated
{
    /**
     * Handle an incoming request.
     *
     * @param string|null ...$guards
     * @return mixed
     */
    public function handle(Request $request, Closure $next, string ...$guards): Response
    {
        $guards = empty($guards) ? [null] : $guards;

        foreach ($guards as $guard) {
            if (Auth::guard($guard)->check()) {
                if ($guard === 'member') {
                    return redirect()->to($request->tenant->tree_url);
                }

                if ($guard === 'admin') {
                    return redirect()->route('admin.tenants.index');
                }

                return redirect()->route('branches.show');
            }
        }

        return $next($request);
    }
}
