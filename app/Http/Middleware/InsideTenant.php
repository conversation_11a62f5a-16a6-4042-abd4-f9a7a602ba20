<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use RuntimeException;
use Symfony\Component\HttpFoundation\Response;

class InsideTenant
{
    public function handle(Request $request, Closure $next): Response
    {
        abort_if(!tenant() && !user(), 404);

        if (!session('tenant_id')) {
            session(['tenant_id' => tenant()->id]);
        }

        collect($request->route()->parameters())->each(function ($resource) {
            if (!$resource instanceof Model) {
                return;
            }

            if (!$resource->tenant_id) {
                throw new RuntimeException('resource has no tenant');
            }

            abort_if((int) user()->tenant_id !== (int) $resource->tenant_id, 404);
        });

        return $next($request);
    }
}
