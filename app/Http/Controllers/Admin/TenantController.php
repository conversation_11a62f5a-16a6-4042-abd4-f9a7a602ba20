<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Tenant;
use App\Models\User;
use Illuminate\Http\Request;

use function session;

class TenantController extends Controller
{
    public function index(Request $request)
    {
        return inertia('Admin/Tenant/Index', [
            'tenants' => Tenant::query()
                ->when(
                    $request->text,
                    fn($query) => $query->where(
                        fn($query) => $query
                            ->where('id', 'like', "%$request->text%")
                            ->orWhereHas(
                                'owner',
                                fn($query) => $query
                                    ->where('name', 'like', "%$request->text%")
                                    ->orWhere('email', 'like', "%$request->text%")
                                    ->orWhere('mobile', 'like', "%$request->text%")
                            )
                            ->orWhereHas('branches', fn($query) => $query->where('name', 'like', "%$request->text%"))
                            ->orWhereHas('settings', fn($query) => $query->where('value', 'like', "%$request->text%"))
                    )
                )
                ->with(['branches', 'owner' => fn($query) => $query->without('tenant')])
                ->paginate(),
        ]);
    }

    public function destroy(Tenant $tenant)
    {
        if ($tenant->nodes()->count() > 50) {
            invalidate('لا يمكن حذف المستخدم لوجود أكثر من 50 فرد');
        }

        $tenant->delete();

        return redirect()
            ->route('admin.tenants.index')
            ->with(['success' => 'تم حذف المستخدم بنجاح']);
    }

    public function login(User $user)
    {
        auth('web')->logout();

        auth('web')->login($user);

        session(['tenant_id' => $user->tenant_id]);

        return redirect()->route('branches.show');
    }

    public function update(Request $request, Tenant $tenant)
    {
        $validated = $request->validate([
            'max_nodes_number' => ['required', 'integer', 'min:1'],
        ]);

        $tenant->update($validated);

        return redirect()
            ->back()
            ->with(['success' => 'تم تحديث العدد المسموح بنجاح']);
    }
}
