<?php

namespace App\Http\Controllers;

use App\Models\Setting;

class FamilyHomeController extends Controller
{
    public function __invoke()
    {
        return inertia('Home', [
            'family_name' => tenant()->getSetting(Setting::FAMILY_NAME),
            'logo' => tenant()->getSetting(Setting::FAMILY_LOGO),
            'about' => tenant()->getSetting(Setting::ABOUT),
            'socials' => [
                'snapchat' => tenant()->getSetting(Setting::SNAPCHAT_LINK),
                'instagram' => tenant()->getSetting(Setting::INSTAGRAM_LINK),
                'facebook' => tenant()->getSetting(Setting::FACEBOOK_LINK),
                'twitter' => tenant()->getSetting(Setting::TWITTER_LINK),
                'whatsapp' => tenant()->getSetting(Setting::WHATSAPP_LINK),
                'youtube' => tenant()->getSetting(Setting::YOUTUBE_LINK),
            ],
        ]);
    }
}
