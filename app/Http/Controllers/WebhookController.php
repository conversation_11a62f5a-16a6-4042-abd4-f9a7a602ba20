<?php

namespace App\Http\Controllers;

use App\Models\Invoice;
use App\Models\Package;
use App\Models\Tenant;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class WebhookController extends Controller
{
    public function moyasar(Request $request)
    {
        // Log the webhook for debugging
        Log::info('Moyasar webhook received', [
            'headers' => $request->headers->all(),
            'payload' => $request->all(),
        ]);

        // Verify webhook signature (Moyasar uses HMAC-SHA256)
        if (!$this->verifyMoyasarSignature($request)) {
            Log::warning('Invalid Moyasar webhook signature');
            return response()->json(['error' => 'Invalid signature'], 401);
        }

        $payload = $request->all();
        
        // Handle different event types
        switch ($payload['type'] ?? null) {
            case 'payment_paid':
                return $this->handlePaymentPaid($payload['data'] ?? []);
            case 'payment_failed':
                return $this->handlePaymentFailed($payload['data'] ?? []);
            default:
                Log::info('Unhandled Moyasar webhook event type', ['type' => $payload['type'] ?? 'unknown']);
                return response()->json(['message' => 'Event type not handled'], 200);
        }
    }

    private function verifyMoyasarSignature(Request $request): bool
    {
        $signature = $request->header('X-Moyasar-Signature');
        if (!$signature) {
            return false;
        }

        $payload = $request->getContent();
        $secret = config('services.moyasar.webhook_secret');
        
        if (!$secret) {
            Log::warning('Moyasar webhook secret not configured');
            return false;
        }

        $expectedSignature = hash_hmac('sha256', $payload, $secret);
        
        return hash_equals($expectedSignature, $signature);
    }

    private function handlePaymentPaid(array $paymentData): \Illuminate\Http\JsonResponse
    {
        try {
            return DB::transaction(function () use ($paymentData) {
                $paymentId = $paymentData['id'] ?? null;
                
                if (!$paymentId) {
                    Log::error('Payment ID missing from webhook data');
                    return response()->json(['error' => 'Payment ID missing'], 400);
                }

                // Check if we already processed this payment
                $existingInvoice = Invoice::where('moyasar_payment_id', $paymentId)->first();
                
                if ($existingInvoice) {
                    if ($existingInvoice->isPaid()) {
                        Log::info('Payment already processed', ['payment_id' => $paymentId]);
                        return response()->json(['message' => 'Payment already processed'], 200);
                    }
                    
                    // Update existing invoice
                    $existingInvoice->markAsPaid('webhook');
                    Log::info('Updated existing invoice via webhook', ['invoice_id' => $existingInvoice->id]);
                    return response()->json(['message' => 'Payment updated'], 200);
                }

                // Get package and tenant information
                $packageId = $paymentData['metadata']['package_id'] ?? null;
                $package = Package::find($packageId);
                
                if (!$package) {
                    Log::error('Package not found', ['package_id' => $packageId]);
                    return response()->json(['error' => 'Package not found'], 400);
                }

                // Find tenant by checking the payment metadata or other means
                // This might need adjustment based on how you identify the tenant
                $tenantId = $paymentData['metadata']['tenant_id'] ?? null;
                if (!$tenantId) {
                    Log::error('Tenant ID missing from payment metadata');
                    return response()->json(['error' => 'Tenant ID missing'], 400);
                }

                $tenant = Tenant::find($tenantId);
                if (!$tenant) {
                    Log::error('Tenant not found', ['tenant_id' => $tenantId]);
                    return response()->json(['error' => 'Tenant not found'], 400);
                }

                // Verify amount
                if ($paymentData['amount'] !== $package->price * 100) {
                    Log::error('Payment amount mismatch', [
                        'expected' => $package->price * 100,
                        'received' => $paymentData['amount']
                    ]);
                    return response()->json(['error' => 'Amount mismatch'], 400);
                }

                // Create invoice
                $invoice = Invoice::create([
                    'tenant_id' => $tenant->id,
                    'package_id' => $package->id,
                    'amount' => $paymentData['amount'],
                    'currency' => $paymentData['currency'],
                    'moyasar_payment_id' => $paymentId,
                    'status' => 'paid',
                    'processed_via' => 'webhook',
                    'metadata' => $paymentData,
                    'paid_at' => now(),
                ]);

                // Update tenant
                $tenant->update([
                    'is_eligible_for_discount' => false,
                    'max_nodes_number' => $tenant->max_nodes_number + $package->nodes,
                ]);

                // Send notifications
                postHog([
                    'event' => "Payment: {$package->nodes} (Webhook)",
                    [
                        'nodes' => $package->nodes,
                        'price' => $package->price,
                        'package_id' => $package->id,
                        'invoice_id' => $invoice->id,
                        'processed_via' => 'webhook',
                    ],
                ]);

                mailAdmin(
                    title: $package->is_discounted ? "Discounted Payment: {$package->nodes} (Webhook)" : "Payment: {$package->nodes} (Webhook)",
                    body: 'Tenant: ' . $tenant->name . "\n" . "Package: {$package->nodes}\n" . "Price: {$package->price}" . "\n" . "Invoice ID: {$invoice->id}" . "\n" . "Processed via: webhook"
                );

                Log::info('Payment processed successfully via webhook', [
                    'invoice_id' => $invoice->id,
                    'tenant_id' => $tenant->id,
                    'package_id' => $package->id,
                ]);

                return response()->json(['message' => 'Payment processed successfully'], 200);
            });
        } catch (\Exception $e) {
            Log::error('Error processing payment webhook', [
                'error' => $e->getMessage(),
                'payment_data' => $paymentData,
            ]);
            
            return response()->json(['error' => 'Internal server error'], 500);
        }
    }

    private function handlePaymentFailed(array $paymentData): \Illuminate\Http\JsonResponse
    {
        try {
            $paymentId = $paymentData['id'] ?? null;
            
            if (!$paymentId) {
                return response()->json(['error' => 'Payment ID missing'], 400);
            }

            $invoice = Invoice::where('moyasar_payment_id', $paymentId)->first();
            
            if ($invoice) {
                $invoice->markAsFailed();
                Log::info('Payment marked as failed via webhook', ['invoice_id' => $invoice->id]);
            }

            return response()->json(['message' => 'Payment failure processed'], 200);
        } catch (\Exception $e) {
            Log::error('Error processing payment failure webhook', [
                'error' => $e->getMessage(),
                'payment_data' => $paymentData,
            ]);
            
            return response()->json(['error' => 'Internal server error'], 500);
        }
    }
}
