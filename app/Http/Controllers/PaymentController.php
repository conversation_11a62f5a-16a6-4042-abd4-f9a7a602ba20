<?php

namespace App\Http\Controllers;

use App\DTOs\Package;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class PaymentController extends Controller
{
    public function verify(Request $request)
    {
        $request->validate([
            'id' => ['required', 'string'], // Moyasar sends payment_id as 'id'
            'status' => ['required', 'string'],
        ]);

        if ($request->status !== 'paid') {
            return redirect()->route('branches.show')->with('error', 'لم يتم اكمال عملية الدفع');
        }

        // Verify payment with Moyasar
        $response = Http::withBasicAuth(config('services.moyasar.secret_key'), '')->get(
            "https://api.moyasar.com/v1/payments/{$request->id}"
        );

        if (!$response->successful()) {
            return redirect()->route('branches.show')->with('error', 'فشل التحقق من عملية الدفع');
        }

        $payment = $response->json();

        $package = Package::find($payment['metadata']['package_id']);

        if (!$package) {
            return redirect()->route('branches.show')->with('error', 'باقة غير صالحة');
        }

        // Verify amount (amount in halala)
        if ($payment['amount'] !== $package->price * 100) {
            return redirect()->route('branches.show')->with('error', 'مبلغ الدفع غير صحيح');
        }

        if (!tenant()->is_eligible_for_discount && $package->isDiscounted) {
            return redirect()->route('branches.show')->with('error', 'لا يمكن استخدام العرض الخاص');
        }

        tenant()->update([
            'is_eligible_for_discount' => false,
            'max_nodes_number' => tenant()->max_nodes_number + $package->nodes,
        ]);

        postHog([
            'event' => "Payment: {$package->nodes}",
            [
                'nodes' => $package->nodes,
                'price' => $package->price,
                'package_id' => $request->package_id,
            ],
        ]);

        mailAdmin(
            title: $package->isDiscounted ? "Discounted Payment: {$package->nodes}" : "Payment: {$package->nodes}",
            body: 'Tenant: ' . tenant()->name . "\n" . "Package: {$package->nodes}\n" . "Price: {$package->price}"
        );

        return redirect()->route('branches.show')->with('success', 'تم الدفع وإضافة الرصيد بنجاح');
    }
}
