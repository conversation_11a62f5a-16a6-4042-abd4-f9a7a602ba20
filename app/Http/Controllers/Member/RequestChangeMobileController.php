<?php

namespace App\Http\Controllers\Member;

use App\Http\Controllers\Controller;
use App\Models\Node;
use App\Models\NodeChange;
use Arr;
use Illuminate\Http\Request;

class RequestChangeMobileController extends Controller
{
    public function store(Request $request, Node $node)
    {
        $request->validate([
            'new_mobile' => ['required', 'string', 'digits:10', 'unique:nodes,mobile'],
        ]);

        NodeChange::create([
            'node_id' => $node->id,
            'tenant_id' => $node->tenant_id,
            'old_attributes' => Arr::only($node->attributesToArray(), Node::EDITABLE_COLUMNS),
            'new_attributes' => [
                'mobile' => $request->new_mobile,
            ],
        ]);

        postHog(['event' => 'Request Change Mobile']);

        return ok('تم إرسال الطلب لمسؤولي الشجرة بنجاح،');
    }
}
