<?php

namespace App\Http\Controllers\Member\Mobile;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use function inertia;

class TreeChartController extends Controller
{
    public function __invoke()
    {
        return inertia('Member/Mobile/TreeChart/Index', [
            'settings' => [
                'layout_mode' => tenant()?->getSetting(Setting::LAYOUT_MODE),
            ],
        ]);
    }
}
