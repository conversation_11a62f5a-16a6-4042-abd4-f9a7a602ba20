<?php

namespace App\Http\Controllers\Member\Mobile;

use App\Http\Controllers\Controller;
use App\Http\Resources\NodeResource;
use App\Models\Node;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

use function json;

class NodeController extends Controller
{
    public function index()
    {
        return response()->streamJson([
            'nodes' => Node::selectBasicInfo()
                ->with([
                    'wives:id,tenant_id,name,family_name,status,husband_id' => [
                        'husband' => fn($q) => $q->selectBasicInfo(),
                    ],
                    'husbands:id,tenant_id,name,family_name,status,wife_id' => [
                        'wife' => fn($q) => $q->selectBasicInfo(),
                    ],
                ])
                ->lazy(),
        ]);
    }

    public function show(Request $request, Node $node)
    {
        $request->validate([
            'include' => [
                'sometimes',
                'array',
                Rule::in(['children', 'country', 'city', 'district', 'parent', 'wives', 'husbands', 'siblings']),
            ],
        ]);

        return json(data: new NodeResource($node->load($request->include ?? [])));
    }
}
