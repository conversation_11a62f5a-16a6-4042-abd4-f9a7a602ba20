<?php

namespace App\Http\Controllers\Member\Mobile;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use NotificationChannels\Expo\ExpoPushToken;

class ExpoPushTokenController extends Controller
{
    public function __invoke(Request $request)
    {
        $request->validate([
            'token' => ['required', ExpoPushToken::rule()],
        ]);

        auth()
            ->user()
            ->update(['expo_token' => $request->token]);

        return ok();
    }
}
