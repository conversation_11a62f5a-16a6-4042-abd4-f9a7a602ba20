<?php

namespace App\Http\Controllers\Member\Mobile;

use App\Http\Controllers\Controller;
use App\Models\Document;

use function json;
use function request;

class DocumentController extends Controller
{
    public function index()
    {
        $documents = Document::query()
            ->latest()
            ->paginate(request('per_page', 15));

        // Transform documents for mobile API
        $documents->getCollection()->transform(function ($document) {
            return [
                'id' => $document->id,
                'title' => $document->title,
                'description' => $document->description,
                'file_size' => $document->file_size,
                'formatted_file_size' => $document->formatted_file_size,
                'mime_type' => $document->mime_type,
                'file_extension' => $document->file_extension,
                'original_filename' => $document->original_filename,
                'compression_status' => $document->compression_status,
                'is_pdf' => $document->isPdf(),
                'is_image' => $document->isImage(),
                'is_compressed' => $document->isCompressed(),
                'file_url' => $document->file_url,
                'compressed_file_url' => $document->compressed_file_url,
                'download_url' => route('documents.download', $document),
                'created_at' => $document->created_at,
                'updated_at' => $document->updated_at,
            ];
        });

        return json(data: $documents);
    }

    public function show(Document $document)
    {
        return json(data: [
            'id' => $document->id,
            'title' => $document->title,
            'description' => $document->description,
            'file_size' => $document->file_size,
            'formatted_file_size' => $document->formatted_file_size,
            'mime_type' => $document->mime_type,
            'file_extension' => $document->file_extension,
            'original_filename' => $document->original_filename,
            'compression_status' => $document->compression_status,
            'compression_metadata' => $document->compression_metadata,
            'is_pdf' => $document->isPdf(),
            'is_image' => $document->isImage(),
            'is_compressed' => $document->isCompressed(),
            'file_url' => $document->file_url,
            'compressed_file_url' => $document->compressed_file_url,
            'download_url' => route('documents.download', $document),
            'created_at' => $document->created_at,
            'updated_at' => $document->updated_at,
        ]);
    }
}
