<?php

namespace App\Http\Controllers\Member\Mobile;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class PublicNodeFilterController extends Controller
{
    public function index(Request $request, string $url)
    {
        $request->validate([
            'text' => ['required', 'string', 'max:255'],
        ]);

        return json([
            'nodes' => tenant($url)
                ->getMainBranch()
                ->getRoot()
                ->descendantsAndSelf()
                ->search($request->text)
                ->limit(5)
                ->get(),
        ]);
    }
}
