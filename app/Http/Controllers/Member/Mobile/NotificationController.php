<?php

namespace App\Http\Controllers\Member\Mobile;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Notifications\DatabaseNotification;

class NotificationController extends Controller
{
    public function index(Request $request)
    {
        $request->validate([
            'per_page' => ['sometimes', 'integer', 'min:1', 'max:50'],
        ]);

        return json(data: auth()->user()->notifications()->latest()->paginate($request->input('per_page', 15)));
    }

    public function markAsRead(DatabaseNotification $notification)
    {
        $notification->markAsRead();

        return ok('تم تحديث الإشعار بنجاح');
    }

    public function markAllAsRead()
    {
        auth()->user()->unreadNotifications->markAsRead();

        return ok('تم تحديث جميع الإشعارات بنجاح');
    }

    public function unreadNotificationCount()
    {
        return json(data: auth()->user()->unreadNotifications()->count());
    }
}
