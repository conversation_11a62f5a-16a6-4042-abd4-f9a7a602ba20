<?php

namespace App\Http\Controllers\Member\Mobile;

use App\Enums\Gender;
use App\Http\Controllers\Controller;
use App\Models\Node;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class NodeFilterController extends Controller
{
    public function index(Request $request)
    {
        $request->validate([
            'text' => ['required', 'string', 'max:255'],
            'gender' => ['nullable', Rule::enum(Gender::class)],
            'label' => ['nullable', 'string', 'max:255'],
        ]);

        $node = $request->label ? Node::firstWhere('label', $request->label) : tenant()->getMainBranch()->getRoot();

        return json([
            'nodes' => $node
                ->descendantsAndSelf()
                ->when($request->gender, fn($query) => $query->where('gender', $request->gender))
                ->search($request->text)
                ->limit(5)
                ->get(),
        ]);
    }
}
