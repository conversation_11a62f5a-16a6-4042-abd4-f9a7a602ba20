<?php

namespace App\Http\Controllers\Member;

use App\Http\Controllers\Controller;
use App\Http\Requests\NodeFormRequest;
use App\Http\Resources\NodeChangeResource;
use App\Models\Node;
use App\Models\NodeChange;
use Arr;
use Illuminate\Http\Request;

class NodeChangeController extends Controller
{
    public function index(Request $request)
    {
        return json(
            data: NodeChangeResource::collection(
                member()
                    ->nodeChanges()
                    ->with('node')
                    ->latest()
                    ->paginate($request->per_page ?? 15)
            )
        );
    }

    public function store(NodeFormRequest $request, Node $node)
    {
        NodeChange::create([
            'node_id' => $node->id,
            'old_attributes' => Arr::only($node->attributesToArray(), Node::EDITABLE_COLUMNS),
            'new_attributes' => $request->prepareData(),
            'member_id' => auth()->id(),
        ]);

        return ok('تم إرسال طلب التعديل لمسؤولي الشجرة بنجاح');
    }
}
