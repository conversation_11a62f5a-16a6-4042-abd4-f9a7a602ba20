<?php

namespace App\Http\Controllers\Member;

use App\Http\Controllers\Controller;
use App\Models\Node;
use App\Models\NodeChange;
use Arr;
use Illuminate\Http\Request;

class RequestChangeEmailController extends Controller
{
    public function store(Request $request, Node $node)
    {
        $request->validate([
            'new_email' => ['required', 'string', 'email', 'unique:nodes,email'],
        ]);

        NodeChange::create([
            'node_id' => $node->id,
            'tenant_id' => $node->tenant_id,
            'old_attributes' => Arr::only($node->attributesToArray(), Node::EDITABLE_COLUMNS),
            'new_attributes' => [
                'email' => $request->new_email,
            ],
        ]);

        postHog(['event' => 'Request Change Email']);

        return ok('تم إرسال الطلب لمسؤولي الشجرة بنجاح،');
    }
}
