<?php

namespace App\Http\Controllers\Member;

use App\Http\Controllers\Controller;
use App\Http\Requests\NodeFormRequest;
use App\Http\Resources\NodeAdditionResource;
use App\Models\NodeAddition;
use Illuminate\Http\Request;

class NodeAdditionController extends Controller
{
    public function index(Request $request)
    {
        return json(
            data: NodeAdditionResource::collection(
                member()
                    ->nodeAddition()
                    ->with('parent')
                    ->latest()
                    ->paginate($request->per_page ?? 15)
            )
        );
    }

    public function store(NodeFormRequest $request)
    {
        NodeAddition::create([
            'parent_id' => $request->parent_id,
            'node_attributes' => $request->prepareData(),
            'member_id' => auth()->id(),
        ]);

        return ok('تم إرسال طلب الإضافة لمسؤولي الشجرة بنجاح');
    }
}
