<?php

namespace App\Http\Controllers;

use App\Models\Branch;
use App\Models\Guest;
use App\Models\Member;
use App\Models\Node;
use App\Models\Tenant;
use App\Models\User;
use Cache;

class WelcomeController extends Controller
{
    public function index()
    {
        return inertia(
            'Landing/Welcome',
            Cache::flexible(
                key: 'welcome-data',
                ttl: [30, 60 * 5],
                callback: fn() => [
                    'nodesCount' => Node::withoutGlobalScopes()->count(),
                    'tenantsCount' => Tenant::withoutGlobalScopes()->count(),
                    'usersCount' =>
                        User::withoutGlobalScopes()->count() +
                        Member::withoutGlobalScopes()->count() +
                        Guest::withoutGlobalScopes()->count(),
                    'branchesNames' => Branch::withoutGlobalScopes()
                        ->whereHas('nodes', fn($q) => $q->withoutGlobalScopes())
                        ->withCount(['nodes' => fn($q) => $q->withoutGlobalScopes()])
                        ->having('nodes_count', '>=', 200)
                        ->limit(300)
                        ->inRandomOrder()
                        ->get(['name', 'id'])
                        ->map(
                            fn($branch) => [
                                'name' => $branch->name,
                                'id' => $branch->id,
                                'nodeCount' => $branch->nodes_count,
                                'roundedCount' => round($branch->nodes_count / 50) * 50,
                            ]
                        ),
                ]
            )
        );
    }
}
