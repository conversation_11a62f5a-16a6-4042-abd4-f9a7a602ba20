<?php

namespace App\Http\Controllers\Auth;

use App\Enums\IdentifierType;
use App\Http\Controllers\Controller;
use App\Models\OneTimePassword;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class OTPController extends Controller
{
    public function create(Request $request)
    {
        return inertia('Tenant/Auth/OTP', [
            'identifier' => $request->identifier,
            'identifier_type' => $request->identifier_type,
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'identifier' => ['required'],
            'identifier_type' => ['required', Rule::enum(IdentifierType::class)],
            'otp' => ['required'],
        ]);

        OneTimePassword::ensureOtpIsValid(
            $request->identifier,
            IdentifierType::from($request->identifier_type),
            $request->otp
        );

        return redirect()->to(
            session('after_otp_url') .
                '?' .
                http_build_query([
                    'identifier' => $request->identifier,
                    'identifier_type' => $request->identifier_type,
                ])
        );
    }
}
