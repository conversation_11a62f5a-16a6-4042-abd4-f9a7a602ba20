<?php

namespace App\Http\Controllers\Auth\User\Login;

use App\Console\Commands\ResetDemoDataCommand;
use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\LoginService;

class DemoLoginController extends Controller
{
    public function __invoke()
    {
        auth('web')->logout();

        $user = User::firstWhere('mobile', config('app.demo_mobile'));

        if (!$user) {
            (new ResetDemoDataCommand())->handle();
        }

        LoginService::handle($user, 'demo');

        return redirect()->intended(route('branches.show'));
    }
}
