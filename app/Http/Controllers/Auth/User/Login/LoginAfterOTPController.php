<?php

namespace App\Http\Controllers\Auth\User\Login;

use App\Enums\IdentifierType;
use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\LoginService;

class LoginAfterOTPController extends Controller
{
    // fixme: what if the user send email or mobile when hit this route?
    public function __invoke()
    {
        $identifierType = IdentifierType::from(request('identifier_type'));

        $user = User::byIdentifier(request('identifier'), $identifierType);

        if (!$user) {
            return redirect()->route('login.create');
        }

        LoginService::handle(user: $user, type: $identifierType->value);

        return redirect()->intended(route('branches.show'));
    }
}
