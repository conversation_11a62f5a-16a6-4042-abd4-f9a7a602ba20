<?php

namespace App\Http\Controllers\Auth\User\Login;

use App\Enums\IdentifierType;
use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\LoginService;
use Exception;
use Laravel\Socialite\Facades\Socialite;

class GoogleLoginController extends Controller
{
    public function redirect()
    {
        return Socialite::driver('google')->redirect();
    }

    public function callback()
    {
        try {
            $oauthUser = Socialite::driver('google')->user();

            $user = User::byIdentifier($oauthUser->email, IdentifierType::Email);

            session([
                'oauth' => [
                    'type' => 'google',
                    'id' => $oauthUser->id,
                    'name' => $oauthUser->name,
                    'email' => $oauthUser->email,
                ],
            ]);

            if (!$user) {
                return redirect()->route('register.info.create');
            }

            $user->update(['google_id' => $oauthUser->id]);

            LoginService::handle(user: $user, type: session('oauth.type'));

            return redirect()->intended(route('branches.show'));
        } catch (Exception $e) {
            report($e);
            return redirect()
                ->route('login.create')
                ->with('error', 'حدث خطأ أثناء تسجيل الدخول باستخدام جوجل. الرجاء المحاولة مرة أخرى.');
        }
    }
}
