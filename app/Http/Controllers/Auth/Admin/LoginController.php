<?php

namespace App\Http\Controllers\Auth\Admin;

use App\Http\Controllers\Controller;
use App\Models\Admin;
use Hash;
use Illuminate\Http\Request;

class LoginController extends Controller
{
    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|exists:admins,email',
            'password' => 'required',
        ]);

        $admin = Admin::firstWhere('email', $request->email);

        if (!Hash::check($request->password, $admin->password)) {
            return back()->withErrors(trans('auth.failed'));
        }

        auth('admin')->login($admin);

        return redirect()->route('admin.tenants.index');
    }

    public function logout(Request $request)
    {
        auth('admin')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return redirect()->route('welcome');
    }
}
