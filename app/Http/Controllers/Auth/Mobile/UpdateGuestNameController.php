<?php

namespace App\Http\Controllers\Auth\Mobile;

use App\Http\Controllers\Controller;
use App\Models\Guest;
use Illuminate\Http\Request;

class UpdateGuestNameController extends Controller
{
    public function __invoke(Request $request)
    {
        abort_if(!auth()->user() instanceof Guest, 400, 'هذا الإجراء متاح لزوار فقط');

        $request->validate([
            'name' => ['required', 'string', 'max:255'],
        ]);

        auth()
            ->user()
            ->update([
                'name' => $request->name,
            ]);

        return ok('تم تحديث الاسم بنجاح');
    }
}
