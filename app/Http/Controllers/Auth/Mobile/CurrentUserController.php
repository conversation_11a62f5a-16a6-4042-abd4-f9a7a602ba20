<?php

namespace App\Http\Controllers\Auth\Mobile;

use App\Http\Controllers\Controller;
use App\Models\Member;

class CurrentUserController extends Controller
{
    public function __invoke()
    {
        $currentUser = request()->user();

        if ($currentUser instanceof Member) {
            return json([
                'user' => [
                    ...$currentUser->toArray(),
                    'node' => $currentUser->node?->load(['children', 'parent', 'siblings']),
                    'relationships' => $currentUser->node?->getRelationships(),
                ],
            ]);
        }

        return json([
            'user' => $currentUser,
        ]);
    }
}
