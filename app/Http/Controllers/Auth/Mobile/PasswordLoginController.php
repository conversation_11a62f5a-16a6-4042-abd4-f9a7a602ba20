<?php

namespace App\Http\Controllers\Auth\Mobile;

use App\Http\Controllers\Controller;
use App\Models\Guest;
use Illuminate\Http\Request;
use Str;

class PasswordLoginController extends Controller
{
    public function __invoke(Request $request)
    {
        $request->validate([
            'device_name' => ['required', 'string', 'max:255'],
            'url' => ['required', 'string', 'max:255'],
            'password' => ['required', 'string'],
        ]);

        $tenant = tenant($request->url);

        if (!$tenant) {
            return invalidate('الشجرة غير موجودة');
        }

        if (!$tenant->mobileAppEnabled()) {
            return invalidate(
                'الشجرة غير مفعلة في التطبيق، تواصل مع مسؤولي الشجرة لتفعيل التطبيق من خلال إعدادات الموقع.'
            );
        }

        $tenantPassword = $tenant->getSetting('password');

        if (!$tenantPassword) {
            return invalidate('تسجيل الدخول بكلمة المرور غير مفعل لهذه الشجرة.');
        }

        if ($request->input('password') !== $tenantPassword) {
            return invalidate('كلمة المرور غير صحيحة');
        }

        $guest = Guest::firstOrCreate(
            [
                // remove string after fixing device name
                'device_name' => $request->device_name . '_' . Str::random(10),
            ],
            [
                'tenant_id' => $tenant->id,
                'name' => $request->name ?? 'زائر',
            ]
        );

        if ($request->name && $request->name !== $guest->name) {
            $guest->update(['name' => $request->name]);
        }

        $guest->tokens()->delete();

        return json([
            'token' => $guest->createToken($request->device_name)->plainTextToken,
            'guest' => $guest,
        ]);
    }
}
