<?php

namespace App\Http\Controllers\Auth\Mobile;

use App\Http\Controllers\Controller;
use App\Http\Resources\TenantResource;

class GetTenantByUrlController extends Controller
{
    public function __invoke(string $url)
    {
        abort_if(!$url, 404);

        $tenant = tenant($url)?->load('settings');

        return json([
            'tenant' => $tenant ? new TenantResource($tenant) : null,
        ]);
    }
}
