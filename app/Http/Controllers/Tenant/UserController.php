<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class UserController extends Controller
{
    public function indexApi()
    {
        return json([
            'users' => User::query()
                ->when(request('search'), fn($query, $search) => $query->where('name', 'like', "%$search%"))
                ->where('id', '!=', auth()->id())
                ->with('nodes')
                ->get(),
        ]);
    }

    public function index()
    {
        return inertia('Tenant/User/Index', [
            'users' => User::with('nodes')->get(),
        ]);
    }

    public function create()
    {
        return inertia('Tenant/User/Create');
    }

    public function store(Request $request)
    {
        preventEditingDemo();

        $request->validate([
            'name' => ['required', 'max:255'],
            'mobile' => $request->mobile ? ['required', 'numeric', Rule::unique('users', 'mobile')] : ['nullable'],
            'email' => $request->email ? ['required', 'email', Rule::unique('users', 'email')] : ['nullable'],
            'node_id' => ['nullable'],
        ]);

        if (!$request->mobile && !$request->email) {
            invalidate('يجب إدخال رقم الجوال أو البريد الإلكتروني');
        }

        $user = User::create([
            'name' => $request->name,
            'mobile' => $request->mobile,
            'email' => $request->email,
        ]);

        $user->nodes()->sync($request->node_id ?? tenant()->getMainBranch()->getRoot());

        postHog(['event' => 'User: Created']);

        return redirect()
            ->route('users.index')
            ->with(['success' => success_msg('المستخدم', 'إنشاء')]);
    }

    public function edit(User $user)
    {
        return inertia('Tenant/User/Edit', [
            'userProp' => $user->load(['nodes']),
        ]);
    }

    public function update(Request $request, User $user)
    {
        preventEditingDemo();

        $request->validate([
            'name' => 'required|max:255',
            'mobile' => $request->mobile
                ? ['required', 'numeric', Rule::unique('users', 'mobile')->ignoreModel($user)]
                : ['nullable'],
            'email' => $request->email
                ? ['required', 'email', Rule::unique('users', 'email')->ignoreModel($user)]
                : ['nullable'],
            'node_id' => ['nullable'],
        ]);

        if (!$request->mobile && !$request->email) {
            invalidate('يجب إدخال رقم الجوال أو البريد الإلكتروني');
        }

        $user->update([
            'name' => $request->name,
            'mobile' => $request->mobile,
            'email' => $request->email,
        ]);

        $user->nodes()->sync($request->node_id ?? tenant()->getMainBranch()->getRoot());

        postHog(['event' => 'User: Updated']);

        return redirect()
            ->route('users.index')
            ->with(['success' => success_msg('المستخدم', 'تعديل')]);
    }

    public function destroy(User $user)
    {
        if ($user->is_owner) {
            return back()->withErrors('لا يمكن حذف حساب مالك النظام');
        }

        $user->nodes()->detach();

        $user->delete();

        postHog(['event' => 'User: Deleted']);

        return redirect()
            ->route('users.index')
            ->with(['success' => success_msg('المستخدم', 'حذف')]);
    }
}
