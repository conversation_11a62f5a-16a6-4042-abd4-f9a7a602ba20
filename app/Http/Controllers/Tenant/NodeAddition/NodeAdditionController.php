<?php

namespace App\Http\Controllers\Tenant\NodeAddition;

use App\Enums\RequestStatus;
use App\Http\Controllers\Controller;
use App\Models\NodeAddition;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class NodeAdditionController extends Controller
{
    public function index(Request $request)
    {
        $request->validate([
            'status' => ['nullable', Rule::in(RequestStatus::values())],
        ]);

        return inertia('Tenant/NodeAddition/Index', [
            'nodeAdditions' => NodeAddition::query()
                ->withWhereHas('parent')
                ->with('member.node')
                ->when($request->status, fn($query) => $query->where('status', $request->status))
                ->latest()
                ->paginate()
                ->appends($request->query()),
        ]);
    }
}
