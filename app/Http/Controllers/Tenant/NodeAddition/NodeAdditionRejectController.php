<?php

namespace App\Http\Controllers\Tenant\NodeAddition;

use App\Enums\RequestStatus;
use App\Http\Controllers\Controller;
use App\Models\NodeAddition;
use App\Notifications\RequestStatusChanged;

class NodeAdditionRejectController extends Controller
{
    public function __invoke(NodeAddition $nodeAddition)
    {
        $nodeAddition->reject();

        // Send notification to the member who created the request
        if ($nodeAddition->member) {
            $nodeAddition->member->notify(new RequestStatusChanged($nodeAddition, RequestStatus::rejected));
        }

        postHog(['event' => 'NodeAddition: rejected']);

        return back()->with(['success' => 'تم رفض الإضافة بنجاح']);
    }
}
