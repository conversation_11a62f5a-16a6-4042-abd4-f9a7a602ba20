<?php

namespace App\Http\Controllers\Tenant\NodeAddition;

use App\Http\Controllers\Controller;
use App\Models\NodeAddition;
use Rap2hpoutre\FastExcel\Facades\FastExcel;

class NodeAdditionExportController extends Controller
{
    public function __invoke()
    {
        postHog(['event' => 'Export Node Additions']);

        return FastExcel::data(NodeAddition::query()->with('parent')->get())->download(
            'طلبات الإضافة.xlsx',
            fn(NodeAddition $nodeChange) => [
                'رقم الأب' => $nodeChange->parent->id,
                'اسم الأب' => $nodeChange->parent->name,
                'اسم الفرد' => $nodeChange->node_attributes['name'] ?? '',
                'جوال الفرد' => $nodeChange->node_attributes['mobile'] ?? '',
                'حالة الطلب' => $nodeChange->status_ar,
            ]
        );
    }
}
