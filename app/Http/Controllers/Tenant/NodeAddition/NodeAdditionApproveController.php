<?php

namespace App\Http\Controllers\Tenant\NodeAddition;

use App\Enums\RequestStatus;
use App\Http\Controllers\Controller;
use App\Models\NodeAddition;
use App\Notifications\RequestStatusChanged;

class NodeAdditionApproveController extends Controller
{
    public function __invoke(NodeAddition $nodeAddition)
    {
        if (tenant()->did_reach_max_number_of_nodes) {
            invalidate('لقد تجاوزت الحد الأقصى لعدد الأفراد.');
        }

        $nodeAddition->approve();

        // Send notification to the member who created the request
        if ($nodeAddition->member) {
            $nodeAddition->member->notify(new RequestStatusChanged($nodeAddition, RequestStatus::approved));
        }

        postHog(['event' => 'Node Addition: Approved']);

        return back()->with(['success' => 'تم قبول الإضافة بنجاح']);
    }
}
