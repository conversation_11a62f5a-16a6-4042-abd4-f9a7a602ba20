<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Jobs\CompressPdfJob;
use App\Models\Document;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class DocumentController extends Controller
{
    public function index()
    {
        return inertia('Tenant/Document/Index', [
            'documents' => Document::latest()->paginate(15),
        ]);
    }

    public function create()
    {
        return inertia('Tenant/Document/Create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:1000'],
            'file' => ['required', 'file', 'max:10240'], // 10MB max
        ]);

        $file = $request->file('file');
        $originalFilename = $file->getClientOriginalName();
        $mimeType = $file->getMimeType();
        $fileSize = $file->getSize();

        // Store the file
        $filePath = $file->store('documents');

        // Create document record
        $document = Document::create([
            'title' => $request->input('title'),
            'description' => $request->input('description'),
            'file_path' => $filePath,
            'file_size' => $fileSize,
            'mime_type' => $mimeType,
            'original_filename' => $originalFilename,
        ]);

        // Dispatch compression job for PDFs
        if ($document->isPdf()) {
            CompressPdfJob::dispatch($document);
        }

        postHog(['event' => 'Document created', [
            'document_id' => $document->id,
            'file_type' => $mimeType,
            'file_size' => $fileSize,
        ]]);

        return redirect()->route('documents.index')->with('success', 'تم رفع المستند بنجاح');
    }

    public function show(Document $document)
    {
        return inertia('Tenant/Document/Show', [
            'document' => $document,
        ]);
    }

    public function edit(Document $document)
    {
        return inertia('Tenant/Document/Edit', [
            'document' => $document,
        ]);
    }

    public function update(Request $request, Document $document)
    {
        $request->validate([
            'title' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:1000'],
            'file' => ['nullable', 'file', 'max:10240'], // 10MB max
        ]);

        $data = [
            'title' => $request->input('title'),
            'description' => $request->input('description'),
        ];

        // Handle file replacement
        if ($request->hasFile('file')) {
            $file = $request->file('file');
            $originalFilename = $file->getClientOriginalName();
            $mimeType = $file->getMimeType();
            $fileSize = $file->getSize();

            // Delete old files
            if ($document->file_path) {
                Storage::delete($document->file_path);
            }
            if ($document->compressed_file_path) {
                Storage::delete($document->compressed_file_path);
            }

            // Store new file
            $filePath = $file->store('documents');

            $data = array_merge($data, [
                'file_path' => $filePath,
                'file_size' => $fileSize,
                'mime_type' => $mimeType,
                'original_filename' => $originalFilename,
                'compressed_file_path' => null,
                'compression_status' => 'pending',
                'compression_metadata' => null,
            ]);

            // Dispatch compression job for PDFs
            if ($mimeType === 'application/pdf') {
                CompressPdfJob::dispatch($document);
            }
        }

        $document->update($data);

        postHog(['event' => 'Document updated', [
            'document_id' => $document->id,
        ]]);

        return redirect()->route('documents.index')->with('success', 'تم تعديل المستند بنجاح');
    }

    public function destroy(Document $document)
    {
        // Delete files
        if ($document->file_path) {
            Storage::delete($document->file_path);
        }
        if ($document->compressed_file_path) {
            Storage::delete($document->compressed_file_path);
        }

        $document->delete();

        postHog(['event' => 'Document deleted', [
            'document_id' => $document->id,
        ]]);

        return redirect()->route('documents.index')->with('success', 'تم حذف المستند بنجاح');
    }

    public function download(Document $document)
    {
        $filePath = $document->compressed_file_path ?: $document->file_path;

        if (!Storage::exists($filePath)) {
            abort(404, 'File not found');
        }

        return Storage::download($filePath, $document->original_filename);
    }
}
