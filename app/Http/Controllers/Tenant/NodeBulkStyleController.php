<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Jobs\UpdateNodeStyleJob;
use App\Models\Node;
use Illuminate\Http\Request;

class NodeBulkStyleController extends Controller
{
    public function __invoke(Request $request, Node $node)
    {
        $request->validate([
            'nodes' => ['required', 'array'],
            'nodes.*.id' => ['required', 'exists:nodes,id'],
            'nodes.*.style' => ['nullable', 'array'],
            'nodes.*.style.x' => ['nullable', 'numeric'],
            'nodes.*.style.y' => ['nullable', 'numeric'],
            'nodes.*.style.scale' => ['nullable', 'numeric'],
            'nodes.*.style.rotation' => ['nullable', 'numeric'],
            'nodes.*.bg_color' => ['nullable', 'string', 'regex:/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/'],
        ]);

        foreach ($request->nodes as $node) {
            UpdateNodeStyleJob::dispatch($node['id'], $node['style'] ?? [], $node['bg_color'] ?? null);
        }

        return back()->with([
            'success' => 'تم التحديث بنجاح',
        ]);
    }
}
