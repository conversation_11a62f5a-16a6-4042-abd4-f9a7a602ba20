<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Node;
use App\Services\ProcessNode;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class NodeStyleController extends Controller
{
    public function __invoke(Request $request, Node $node)
    {
        $request->validate([
            'style' => ['nullable', 'array'],
            'style.x' => ['nullable', 'numeric'],
            'style.y' => ['nullable', 'numeric'],
            'style.scale' => ['nullable', 'numeric'],
            'style.rotation' => ['nullable', 'numeric'],
            'bg_color' => ['nullable', 'string', 'regex:/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/'],
        ]);

        DB::transaction(function () use ($node, $request) {
            $node->update([
                'style' => [...$node->style ?? ProcessNode::initialNodeStyle($node->parent), ...$request->style ?? []],
                'bg_color' => $request->bg_color ?? $node->bg_color,
            ]);
        }, 5);

        return ok();
    }
}
