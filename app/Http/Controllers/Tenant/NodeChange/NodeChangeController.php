<?php

namespace App\Http\Controllers\Tenant\NodeChange;

use App\Enums\RequestStatus;
use App\Http\Controllers\Controller;
use App\Models\NodeChange;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class NodeChangeController extends Controller
{
    public function index(Request $request)
    {
        $request->validate([
            'status' => ['nullable', Rule::in(RequestStatus::values())],
        ]);

        return inertia('Tenant/NodeChange/Index', [
            'nodeChanges' => NodeChange::query()
                ->with(['node', 'member.node'])
                ->when($request->status, fn($query) => $query->where('status', $request->status))
                ->latest()
                ->paginate()
                ->appends($request->query()),
        ]);
    }
}
