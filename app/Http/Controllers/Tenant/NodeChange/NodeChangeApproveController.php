<?php

namespace App\Http\Controllers\Tenant\NodeChange;

use App\Enums\RequestStatus;
use App\Http\Controllers\Controller;
use App\Models\NodeChange;
use App\Notifications\RequestStatusChanged;

class NodeChangeApproveController extends Controller
{
    public function __invoke(NodeChange $nodeChange)
    {
        $nodeChange->approve();

        // Send notification to the member who created the request
        if ($nodeChange->member) {
            $nodeChange->member->notify(new RequestStatusChanged($nodeChange, RequestStatus::approved));
        }

        postHog(['event' => 'Node Change: Approved']);

        return back()->with(['success' => 'تم تعديل الفرد بنجاح']);
    }
}
