<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Line;
use App\Models\Node;
use App\Services\ProcessNode;
use DB;
use Illuminate\Http\Request;
use function back;

class SetNodeInitialStyleController extends Controller
{
    public function __invoke(Request $request, Node $node)
    {
        if ($node->style) {
            return back();
        }

        $line = DB::transaction(function () use ($node) {
            $initialStyle = ProcessNode::initialNodeStyle($node->parent);

            $node->update([
                'style' => $initialStyle,
            ]);

            return Line::create([
                'from_node_id' => $node->parent_id,
                'to_node_id' => $node->id,
                'points' => ProcessNode::initialLineStyle($initialStyle),
            ]);
        });

        return back()->with([
            'success' => 'تمت إضافة الفرد بنجاح',
            'data' => [
                'node' => $node->load(['children']),
                'line' => $line,
            ],
        ]);
    }
}
