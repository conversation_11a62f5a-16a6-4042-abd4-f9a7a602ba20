<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Node;
use Illuminate\Http\Request;

class NodeListController extends Controller
{
    public function index(Request $request)
    {
        $request->validate([
            'name' => ['nullable', 'string'],
        ]);

        $query = Node::when($request->name, fn($q) => $q->search($request->name));

        return inertia('Tenant/NodeList/Index', [
            'nodes' => $query
                ->with(['country', 'city', 'district'])
                ->latest()
                ->paginate(15)
                ->appends($request->query()),
        ]);
    }
}
