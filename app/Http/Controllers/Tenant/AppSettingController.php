<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\NodeVisibility;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use function success_msg;

class AppSettingController extends Controller
{
    public function index()
    {
        return inertia('Tenant/AppSettings/Edit', [
            'settings' => tenant()->settings,
            'nodeAttributes' => NodeVisibility::NODE_ATTRIBUTES,
        ]);
    }

    public function update(Request $request)
    {
        preventEditingDemo();

        $newSettings = $request->validate([
            'url' => ['sometimes', 'max:255', 'regex:/^[a-z](-?[a-z])*$/'],
            'default_node_visibility' => ['sometimes', 'array', Rule::in(NodeVisibility::NODE_ATTRIBUTES)],
            'hide_females_in_public_page' => ['sometimes', 'boolean'],
            'allow_guests' => ['sometimes', 'boolean'],
            'mobile_app_enabled' => ['sometimes', 'boolean'],
            'paper_tree_url' => ['sometimes', 'nullable', 'extensions:pdf', 'max:30720'],
            'password' => ['sometimes', 'nullable', 'string', 'min:6'],
        ]);

        if (!tenant()->has_main_domain && Setting::isUrlTaken($request->url, tenant())) {
            invalidate(['url' => 'الرابط مأخوذ']);
        }

        Setting::setMany($newSettings);

        postHog(['event' => 'Settings Updated']);

        return redirect()
            ->route('settings.app.index')
            ->with(['success' => success_msg('الإعدادات', 'تعديل')]);
    }
}
