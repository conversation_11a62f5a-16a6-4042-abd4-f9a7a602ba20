<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Line;
use DB;
use Illuminate\Http\Request;
use function ok;

class LineController extends Controller
{
    public function index()
    {
        return response()->streamJson([
            'lines' => Line::cursor(),
        ]);
    }

    public function update(Request $request, Line $line)
    {
        $request->validate([
            'points' => ['required', 'array'],
        ]);

        DB::transaction(fn() => $line->update(['points' => $request->points]), 5);

        return ok();
    }
}
