<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Post;
use Illuminate\Http\Request;
use Storage;
use Str;

class PostController extends Controller
{
    public function index()
    {
        return inertia('Tenant/Post/Index', [
            'posts' => Post::paginate(),
        ]);
    }

    public function create()
    {
        return inertia('Tenant/Post/Create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => ['required'],
            'content' => ['required'],
            'image' => ['image'],
            'should_publish' => ['nullable', 'boolean'],
            'start_at' => ['nullable', 'date'],
            'end_at' => ['nullable', 'date', 'after:start_at'],
            'links' => ['nullable', 'array'],
            'links.*.label' => ['required', 'string'],
            'links.*.value' => ['required', 'url'],
        ]);

        Post::create([
            'title' => $request->input('title'),
            'content' => $request->input('content'),
            'slug' => Str::slug($request->input('title')),
            'author_id' => auth()->id(),
            'image' => $request->file('image')?->store('posts-images'),
            'published_at' => $request->boolean('should_publish') ? now() : null,
            'start_at' => $request->input('start_at'),
            'end_at' => $request->input('end_at'),
            'links' => $request->input('links', []),
        ]);

        postHog(['event' => 'Post created']);

        return redirect()->route('posts.index')->with('success', 'تم إضافة المنشور بنجاح');
    }

    public function edit(Post $post)
    {
        return inertia('Tenant/Post/Edit', [
            'post' => $post,
        ]);
    }

    public function update(Request $request, Post $post)
    {
        $request->validate([
            'title' => ['required'],
            'content' => ['required'],
            'image' => ['nullable', 'image'],
            'should_publish' => ['nullable', 'boolean'],
            'start_at' => ['nullable', 'date'],
            'end_at' => ['nullable', 'date', 'after:start_at'],
            'links' => ['nullable', 'array'],
            'links.*.label' => ['required', 'string'],
            'links.*.value' => ['required', 'url'],
        ]);

        if ($request->file('image') && $post->image) {
            Storage::delete($post->image);
        }

        $post->update([
            'title' => $request->input('title'),
            'content' => $request->input('content'),
            'slug' => Str::slug($request->input('title')),
            'image' => $request->file('image') ? $request->file('image')->store('posts-images') : $post->image,
            'published_at' => $request->input('should_publish') ? now() : null,
            'start_at' => $request->input('start_at'),
            'end_at' => $request->input('end_at'),
            'links' => $request->input('links', []),
        ]);

        postHog(['event' => 'Post updated']);

        return redirect()->route('posts.index')->with('success', 'تم تعديل المنشور بنجاح');
    }

    public function destroy(Post $post)
    {
        $post->delete();

        postHog(['event' => 'Post deleted']);

        return redirect()->route('posts.index')->with('success', 'تم حذف المنشور بنجاح');
    }
}
