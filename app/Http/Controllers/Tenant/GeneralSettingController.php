<?php

namespace App\Http\Controllers\Tenant;

use App\Enums\LayoutMode;
use App\Http\Controllers\Controller;
use App\Models\NodeVisibility;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use function success_msg;

class GeneralSettingController extends Controller
{
    public function index()
    {
        return inertia('Tenant/GeneralSettings/Edit', [
            'settings' => tenant()->settings,
            'nodeAttributes' => NodeVisibility::NODE_ATTRIBUTES,
        ]);
    }

    public function update(Request $request)
    {
        preventEditingDemo();

        $newSettings = $request->validate([
            'family_name' => ['sometimes', 'max:255'],
            'about' => ['sometimes', 'nullable', 'max:5000', 'string'],
            'family_logo' => ['sometimes', 'nullable', 'image', 'max:5120'],
            'snapchat_link' => ['sometimes', 'nullable', 'max:255', 'url'],
            'instagram_link' => ['sometimes', 'nullable', 'max:255', 'url'],
            'facebook_link' => ['sometimes', 'nullable', 'max:255', 'url'],
            'twitter_link' => ['sometimes', 'nullable', 'max:255', 'url'],
            'whatsapp_link' => ['sometimes', 'nullable', 'max:255', 'url'],
            'youtube_link' => ['sometimes', 'nullable', 'max:255', 'url'],
            'layout_mode' => ['sometimes', 'string', Rule::enum(LayoutMode::class)],
        ]);

        Setting::setMany($newSettings);

        postHog(['event' => 'Settings: General']);

        return back()->with(['success' => success_msg('الإعدادات', 'تعديل')]);
    }
}
