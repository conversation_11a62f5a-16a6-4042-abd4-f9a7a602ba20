<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Node;
use App\Models\Setting;
use Rap2hpoutre\FastExcel\Facades\FastExcel;

class NodeExportController extends Controller
{
    public function index()
    {
        $nodes = Node::query()
            ->with(['country:id,name', 'city:id,name', 'district:id,name'])
            ->get();

        postHog(['event' => 'Export Nodes']);

        return FastExcel::data($nodes)->download(
            (tenant()->getSetting(Setting::FAMILY_NAME) ?? 'شجرة العائلة') . '.xlsx',
            fn(Node $node) => [
                'الرقم' => $node->id,
                'رقم الأب' => $node->parent_id,
                'الاسم' => $node->name,
                'الفرع' => $node->label,
                'الاسم الكامل' => $node->full_name,
                'الجوال' => $node->mobile,
                'نبذة' => $node->about,
                'اللقب' => $node->nickname,
                'تاريخ الميلاد' => $node->hijri_birth_date,
                'تاريخ الوفاة' => $node->hijri_death_date,
                'الحالة' => $node->life_status->translate(),
                'الجنس' => $node->gender->translate(),
                'الدولة' => $node->country?->name,
                'المدينة' => $node->city?->name,
                'الحي' => $node->district?->name,
            ]
        );
    }
}
