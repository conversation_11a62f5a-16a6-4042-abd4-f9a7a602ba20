<?php

namespace App\Http\Controllers\Tenant\Branch;

use App\Http\Controllers\Controller;
use App\Http\Requests\NodeFormRequest;
use App\Http\Resources\NodeResource;
use App\Models\Node;
use App\Services\ProcessNode;
use Illuminate\Database\Eloquent\Collection;
use function json;
use function response;

class NodeController extends Controller
{
    public function index()
    {
        return response()->streamJson([
            'nodes' => Node::selectBasicInfo()
                ->with([
                    'wives:id,tenant_id,name,family_name,status,husband_id,wife_id' => [
                        'husband' => fn($q) => $q->selectBasicInfo(),
                    ],
                    'husbands:id,tenant_id,name,family_name,status,wife_id,husband_id' => [
                        'wife' => fn($q) => $q->selectBasicInfo(),
                    ],
                    'otherParentRelationship',
                ])
                ->lazy(),
        ]);
    }

    public function show(Node $node)
    {
        return json(
            data: new NodeResource(
                $node
                    ->load(['otherParentRelationship', 'parent', 'children', 'siblings', 'country', 'city', 'district'])
                    ->loadRelationships()
            )
        );
    }

    public function children(Node $node)
    {
        $children = $node->loadRelationships()->load('children.otherParentRelationship')->children;

        return response()->json(
            $children->loadCount('children')->map(
                fn(Node $child) => [
                    'data' => $child,
                    'isBatch' => $child->children_count,
                ]
            )
        );
    }

    public function store(NodeFormRequest $request)
    {
        if (tenant()->did_reach_max_number_of_nodes) {
            postHog(['event' => 'Node: Created - Maximum Reached']);
            mailAdmin(
                title: 'Maximum Nodes Reached',
                body: "Tenant: {$request->user()->tenant->name}\n" .
                    "Tenant ID: {$request->user()->tenant->id}\n" .
                    "Tenant URL: {$request->user()->tenant->tree_url}"
            );

            invalidate('لقد تجاوزت الحد الأقصى لعدد الأفراد.');
        }

        ['node' => $node, 'line' => $line] = ProcessNode::handle($request->prepareData());

        postHog(['event' => 'Node: Created']);

        $node->loadRelationships()->load(['country', 'city', 'district']);

        // set children empty even when it's a new node
        $node->setRelation('children', Collection::make());

        return back()->with([
            'success' => 'تم إضافة الفرد بنجاح',
            'data' => [
                'node' => $node->loadRelationships()->load(['country', 'city', 'district']),
                'line' => $line,
            ],
        ]);
    }

    public function update(NodeFormRequest $request, Node $node)
    {
        ['node' => $node] = ProcessNode::handle($request->prepareData(), $node);

        postHog(['event' => 'Node: Updated']);

        return back()->with([
            'success' => 'تم تعديل الفرد بنجاح',
            'data' => [
                'node' => $node->load([
                    'wives:id,tenant_id,name,family_name,status,husband_id,wife_id' => [
                        'husband' => fn($q) => $q->selectBasicInfo(),
                    ],
                    'husbands:id,tenant_id,name,family_name,status,wife_id,husband_id' => [
                        'wife' => fn($q) => $q->selectBasicInfo(),
                    ],
                    'otherParentRelationship',
                ]),
            ],
        ]);
    }

    public function destroy(Node $node)
    {
        if ($node->is_root) {
            invalidate('لا يمكن حذف الجذر');
        }

        $descendantsCount = $node->descendants()->count();

        if ($descendantsCount > 100) {
            invalidate(
                "لا يمكن حذف هذا الفرد لأنه لديه {$descendantsCount} من الأبناء والأحفاد وهو أكثر من الحد المسموح به (100)."
            );
        }

        $node->delete();

        postHog([
            'event' => 'Node: Deleted',
            'descendants_count' => $descendantsCount,
        ]);

        return back()->with('success', "تم حذف الفرد وجميع أبنائه ({$descendantsCount} فرد) بنجاح");
    }
}
