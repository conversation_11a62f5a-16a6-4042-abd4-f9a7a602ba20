<?php

namespace App\Http\Controllers\Tenant\Branch;

use App\Http\Controllers\Controller;
use App\Models\Node;

class UpdateAddedToPaperAtController extends Controller
{
    public function update(Node $node)
    {
        $node->update(['added_to_paper_at' => now()]);

        postHog(['event' => 'Update added to paper at']);

        back()->with([
            'success' => 'تم إضافة الفرد إلى الشجرة الورقية بنجاح',
            'data' => [
                'node' => $node->loadRelationships()->load(['country', 'city', 'district']),
            ],
        ]);
    }
}
