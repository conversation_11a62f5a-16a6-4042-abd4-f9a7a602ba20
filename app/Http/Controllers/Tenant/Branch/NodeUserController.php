<?php

namespace App\Http\Controllers\Tenant\Branch;

use App\Http\Controllers\Controller;
use App\Models\Node;
use Illuminate\Http\Request;

use function back;

class NodeUserController extends Controller
{
    public function update(Request $request, Node $node)
    {
        $node->users()->sync($request->users);

        postHog(['event' => 'Node: Attach Users']);

        return back()->with(['success' => 'تم ربط الأفراد بهذا الفرد']);
    }
}
