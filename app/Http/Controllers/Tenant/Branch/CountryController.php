<?php

namespace App\Http\Controllers\Tenant\Branch;

use App\Http\Controllers\Controller;
use App\Models\Country;
use Illuminate\Http\Request;

class CountryController extends Controller
{
    public function index(Request $request)
    {
        $request->validate([
            'text' => ['nullable', 'string', 'max:255'],
        ]);

        return json([
            'countries' => $request->text
                ? Country::query()->whereFuzzy('name', $request->text)->limit(10)->get()
                : Country::query()->where('is_featured', true)->limit(10)->get(),
        ]);
    }
}
