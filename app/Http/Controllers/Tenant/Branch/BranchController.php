<?php

namespace App\Http\Controllers\Tenant\Branch;

use App\Http\Controllers\Controller;
use App\Models\Node;
use function inertia;

class BranchController extends Controller
{
    public function show(?Node $node = null)
    {
        return inertia('Tenant/Branch/Show', [
            'root' => tenant()->getMainBranch()->getRoot(),
            'branch' => tenant()->getMainBranch(),
            'currentNode' => $node?->loadRelationships()->load('children')->loadCount('children'),
            'nodeCount' => tenant()->all_nodes_count,
            'maleNodeCount' => tenant()->rememberCache('male_node_count', fn() => Node::query()->male()->count()),
            'femaleNodeCount' => tenant()->rememberCache('female_node_count', fn() => Node::query()->female()->count()),
        ]);
    }
}
