<?php

namespace App\Http\Controllers\Tenant\Branch;

use App\Http\Controllers\Controller;
use App\Models\Node;

class UpdateAllAddedToPaperAtController extends Controller
{
    public function update()
    {
        Node::query()
            ->whereNull('added_to_paper_at')
            ->update(['added_to_paper_at' => now()]);

        postHog(['event' => 'Add All To Paper']);

        back()->with([
            'success' => 'تم إضافة جميع الأفراد إلى الشجرة الورقية بنجاح',
        ]);
    }
}
