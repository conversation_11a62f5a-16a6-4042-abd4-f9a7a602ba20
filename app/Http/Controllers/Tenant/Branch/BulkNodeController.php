<?php

namespace App\Http\Controllers\Tenant\Branch;

use App\Enums\Gender;
use App\Enums\LifeStatus;
use App\Http\Controllers\Controller;
use App\Services\ProcessNode;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class BulkNodeController extends Controller
{
    public function store(Request $request)
    {
        $request->validate([
            'parent_id' => ['required', 'exists:nodes,id'],
            'nodes' => ['required', 'array', 'min:1'],
            'nodes.*.name' => ['required', 'string', 'max:255'],
            'nodes.*.gender' => ['required', Rule::enum(Gender::class)],
            'nodes.*.life_status' => ['required', Rule::enum(LifeStatus::class)],
        ]);

        // Check if tenant has reached max nodes
        if (tenant()->did_reach_max_number_of_nodes) {
            postHog(['event' => 'Bulk Nodes: Created - Maximum Reached']);
            mailAdmin(
                title: 'Maximum Nodes Reached (Bulk Creation)',
                body: "Tenant: {$request->user()->tenant->name}\n" .
                    "Tenant ID: {$request->user()->tenant->id}\n" .
                    "Tenant URL: {$request->user()->tenant->tree_url}"
            );

            invalidate('لقد تجاوزت الحد الأقصى لعدد الأفراد.');
        }

        // Check if adding these nodes would exceed the max nodes limit
        $currentNodeCount = tenant()->nodes()->count();
        $newNodesCount = count($request->nodes);

        if ($currentNodeCount + $newNodesCount > tenant()->max_nodes_number) {
            postHog(['event' => 'Bulk Nodes: Created - Would Exceed Maximum']);
            invalidate('إضافة هذه المجموعة ستتجاوز الحد الأقصى لعدد الأفراد.');
        }

        $createdNodes = [];
        $createdLines = [];

        foreach ($request->nodes as $nodeData) {
            $data = [
                'name' => $nodeData['name'],
                'gender' => $nodeData['gender'],
                'life_status' => $nodeData['life_status'] ?? 'unknown',
                'parent_id' => $request->parent_id,
                'branch_id' => tenant()->getMainBranch()->id,
            ];

            ['node' => $node, 'line' => $line] = ProcessNode::handle($data);

            $node->loadRelationships();

            // set children empty even when it's a new node
            $node->setRelation('children', Collection::make());

            $createdNodes[] = $node->loadRelationships();
            $createdLines[] = $line;
        }

        postHog(['event' => 'Bulk Nodes: Created', 'count' => count($createdNodes)]);

        return back()->with([
            'success' => 'تم إضافة الأفراد بنجاح',
            'data' => [
                'nodes' => $createdNodes,
                'lines' => $createdLines,
            ],
        ]);
    }
}
