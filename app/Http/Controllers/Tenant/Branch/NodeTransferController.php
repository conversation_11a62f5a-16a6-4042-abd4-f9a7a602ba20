<?php

namespace App\Http\Controllers\Tenant\Branch;

use App\Http\Controllers\Controller;
use App\Jobs\UpdateFullNameJob;
use App\Models\Node;
use Illuminate\Http\Request;
use function inertia;
use function invalidate;

class NodeTransferController extends Controller
{
    public function update(Request $request, Node $node)
    {
        $request->validate([
            'new_parent_id' => ['required', 'exists:nodes,id'],
        ]);

        $newParent = Node::find($request->new_parent_id);

        if ($newParent->descendantsAndSelf()->where('id', $node->id)->exists()) {
            invalidate('لا يمكن نقل الفرد إلى نفسه أو إلى ذريته');
        }

        if ($newParent->is($node->parent)) {
            invalidate('لا يمكن نقل الفرد إلى نفس الأب');
        }

        $node->update(['parent_id' => $request->new_parent_id]);

        $node->descendantsAndSelf()->each(fn(Node $n) => UpdateFullNameJob::dispatch($n));

        inertia()->share([
            'success' => 'تم نقل الفرد بنجاح',
            'data' => [
                'node' => $node->load(['country', 'city', 'district']),
            ],
        ]);

        postHog(['event' => 'Node Transfer']);

        return back();
    }
}
