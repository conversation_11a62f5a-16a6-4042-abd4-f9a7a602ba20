<?php

namespace App\Http\Controllers\Tenant\Branch;

use App\Http\Controllers\Controller;
use App\Models\District;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

use function json;

class DistrictController extends Controller
{
    public function index(Request $request)
    {
        $request->validate([
            'text' => ['required', 'string', 'max:255'],
            'city_id' => ['required', 'integer', Rule::exists('cities', 'id')],
        ]);

        return json([
            'districts' => District::where('city_id', $request->city_id)
                ->whereFuzzy('name', $request->text)
                ->limit(10)
                ->get(),
        ]);
    }
}
