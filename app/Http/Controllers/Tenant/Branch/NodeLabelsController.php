<?php

namespace App\Http\Controllers\Tenant\Branch;

use App\Http\Controllers\Controller;
use App\Models\Node;

class NodeLabelsController extends Controller
{
    public function index()
    {
        return json([
            'labels' => Node::query()
                ->whereNotNull('label')
                ->when(request('text'), fn($query, $text) => $query->whereFuzzy('label', $text))
                ->pluck('label'),
        ]);
    }
}
