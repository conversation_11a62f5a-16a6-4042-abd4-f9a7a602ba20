<?php

namespace App\Http\Controllers\Tenant\Branch;

use App\Http\Controllers\Controller;
use App\Models\Line;
use App\Models\Node;

class ResetAllNodesStyleController extends Controller
{
    public function __invoke()
    {
        Node::query()->update(['style' => null]);
        Line::query()->delete();

        postHog(['event' => 'Reset All Nodes Style']);

        return back()->with([
            'success' => 'تم إزالة ترتيب جميع الأفراد بنجاح',
        ]);
    }
}
