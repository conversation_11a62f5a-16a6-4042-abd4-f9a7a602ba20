<?php

namespace App\Http\Controllers\Tenant\Branch;

use App\Http\Controllers\Controller;
use App\Jobs\UpdateFullNameJob;
use App\Models\Node;

class UpdateAllFullNameController extends Controller
{
    public function __invoke()
    {
        tenant()
            ->getMainBranch()
            ->getRoot()
            ->descendantsAndSelf()
            ->each(fn(Node $n) => UpdateFullNameJob::dispatch($n));

        postHog(['event' => 'Update all full names']);

        return back()->with([
            'success' => 'تم تحديث الأسماء بنجاح (تحديث جميع الأسماء قد يستغرق بعض الوقت)',
        ]);
    }
}
