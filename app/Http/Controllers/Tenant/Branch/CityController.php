<?php

namespace App\Http\Controllers\Tenant\Branch;

use App\Http\Controllers\Controller;
use App\Models\City;
use Illuminate\Http\Request;

class CityController extends Controller
{
    public function index(Request $request)
    {
        $request->validate([
            'text' => ['nullable', 'string', 'max:255'],
        ]);

        return json([
            'cities' => $request->text
                ? City::query()->whereFuzzy('name', $request->text)->limit(10)->get()
                : City::query()->where('is_featured', true)->limit(10)->get(),
        ]);
    }
}
