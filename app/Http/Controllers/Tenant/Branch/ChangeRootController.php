<?php

namespace App\Http\Controllers\Tenant\Branch;

use App\Enums\Gender;
use App\Enums\LifeStatus;
use App\Http\Controllers\Controller;
use App\Jobs\UpdateFullNameJob;
use App\Models\Node;
use DB;
use Illuminate\Http\Request;

class ChangeRootController extends Controller
{
    public function __invoke(Request $request)
    {
        $request->validate([
            'new_parent_name' => ['required', 'string'],
        ]);

        DB::transaction(function () use ($request) {
            $branch = tenant()->getMainBranch();

            $root = $branch->getRoot();

            $newRoot = Node::create([
                'is_root' => true,
                'name' => $request->new_parent_name,
                'parent_id' => null,
                'branch_id' => $branch->id,
                'life_status' => LifeStatus::unknown,
                'gender' => Gender::Male,
                'bg_color' => $root->bg_color,
            ]);

            $newRoot->descendantsAndSelf()->each(fn(Node $node) => UpdateFullNameJob::dispatch($node));

            $root->update([
                'is_root' => false,
                'parent_id' => $newRoot->id,
                'style' => [
                    'x' => 0,
                    'y' => 0,
                ],
            ]);
        });

        postHog(['event' => 'Change Root']);

        return back()->with([
            'success' => 'تم تغيير أصل العائلة بنجاح',
        ]);
    }
}
