<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Node;

class NodeRelativeController extends Controller
{
    public function index(Node $node)
    {
        return json([
            'parent' => $node->parent,
            'other_parent_relationship' => $node->otherParentRelationship?->load(['wife', 'husband']),
            'siblings' => $node->siblings,
            'children' => $node->children,
        ]);
    }
}
