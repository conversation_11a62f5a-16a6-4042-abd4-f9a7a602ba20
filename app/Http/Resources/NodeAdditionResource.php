<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class NodeAdditionResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'parent' => NodeResource::make($this->whenLoaded('parent')),
            'status' => $this->status,
            'status_ar' => $this->status_ar,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
