<?php

namespace App\Http\Resources;

use App\Models\Node;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin Node */
class NodeResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'life_status' => $this->life_status,
            'life_status_ar' => $this->life_status_ar,
            'birth_date' => $this->birth_date,
            'death_date' => $this->death_date,
            'is_root' => $this->is_root,
            'mobile' => $this->mobile,
            'gender' => $this->gender,
            'parent_id' => $this->parent_id,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'deleted_at' => $this->deleted_at,
            'about' => $this->about,
            'full_name' => $this->full_name,
            'nickname' => $this->nickname,
            'label' => $this->label,
            'bg_color' => $this->bg_color,
            'size' => $this->size,
            'order' => $this->order,
            'photo' => $this->photo,
            'added_to_paper_at' => $this->added_to_paper_at,
            'email' => $this->email,
            'style' => $this->style,
            'photo_url' => $this->photo_url,
            'country' => $this->whenLoaded('country'),
            'city' => $this->whenLoaded('city'),
            'district' => $this->whenLoaded('district'),
            'wives' => RelationshipResource::collection($this->whenLoaded('wives')),
            'husbands' => RelationshipResource::collection($this->whenLoaded('husbands')),
            'children' => self::collection($this->whenLoaded('children')),
            'parent' => new self($this->whenLoaded('parent')),
            'other_parent_relationship' => RelationshipResource::make($this->whenLoaded('otherParentRelationship')),
        ];
    }
}
