<?php

namespace App\Http\Resources;

use App\Models\Tenant;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin Tenant */
class TenantResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'has_main_domain' => $this->has_main_domain,
            'main_domain' => $this->main_domain,
            'max_nodes_number' => $this->max_nodes_number,
            'did_reach_max_number_of_nodes' => $this->did_reach_max_number_of_nodes,
            'tree_url' => $this->tree_url,

            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'deleted_at' => $this->deleted_at,

            'settings' => SettingResource::collection($this->whenLoaded('settings')),
        ];
    }
}
