<?php

namespace App\Http\Resources;

use App\Models\Relationship;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin Relationship */
class RelationshipResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'status' => $this->status,
            'name' => $this->name,
            'family_name' => $this->family_name,
            'deleted_at' => $this->deleted_at,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'is_outside_family' => $this->is_outside_family,
            'husband' => $this->whenLoaded('husband'),
            'wife' => $this->whenLoaded('wife'),
        ];
    }
}
