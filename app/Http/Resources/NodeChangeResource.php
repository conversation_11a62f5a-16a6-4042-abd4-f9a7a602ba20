<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class NodeChangeResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'node' => NodeResource::make($this->whenLoaded('node')),
            'old_attributes' => $this->old_attributes,
            'new_attributes' => $this->new_attributes,
            'status' => $this->status,
            'status_ar' => $this->status_ar,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
