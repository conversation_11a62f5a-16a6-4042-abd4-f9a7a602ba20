<?php

namespace App\Http\Requests;

use App\Enums\IdentifierType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class RegisterInfoRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'family_name' => ['required', 'string', 'max:255'],
            'root_name' => ['required', 'string', 'max:255'],
            'identifier' => ['sometimes'],
            'identifier_type' => ['sometimes', Rule::enum(IdentifierType::class)],
        ];
    }
}
