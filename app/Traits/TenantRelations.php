<?php

namespace App\Traits;

use App\Models\Branch;
use App\Models\Line;
use App\Models\Member;
use App\Models\Node;
use App\Models\Setting;
use App\Models\Stat;
use App\Models\Tenant;
use App\Models\User;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

trait TenantRelations
{
    public function settings(): HasMany
    {
        return $this->hasMany(Setting::class);
    }

    public function owner(): HasOne
    {
        return $this->hasOne(User::class)->where('is_owner', true);
    }

    public function branches(): HasMany
    {
        return $this->hasMany(Branch::class);
    }

    public function nodes(): HasMany
    {
        return $this->hasMany(Node::class);
    }

    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    public function referees(): <PERSON><PERSON><PERSON>
    {
        return $this->hasMany(Tenant::class, 'referred_by_id');
    }

    public function members(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Member::class);
    }

    public function stat(): HasOne
    {
        return $this->hasOne(Stat::class);
    }

    public function lines(): Has<PERSON>any
    {
        return $this->hasMany(Line::class);
    }
}
