<?php

namespace App\Traits;

trait Acceptable
{
    public static $PENDING = 'pending';

    public static $ACCEPTED = 'accepted';

    public static $REJECTED = 'rejected';

    protected static function bootAcceptable()
    {
        static::creating(function ($resource) {
            $resource->fill(['state' => $resource->state ?? self::$PENDING]);
        });
    }

    public function accept()
    {
        return tap($this)->fill(['state' => self::$ACCEPTED]);
    }

    public function reject()
    {
        return tap($this)->fill(['state' => self::$REJECTED]);
    }
}
