<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Package extends Model
{
    use HasFactory;

    protected $guarded = ['id'];

    protected function casts(): array
    {
        return [
            'is_discounted' => 'boolean',
            'is_active' => 'boolean',
        ];
    }

    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeDiscounted($query)
    {
        return $query->where('is_discounted', true);
    }

    public function scopeRegular($query)
    {
        return $query->where('is_discounted', false);
    }

    public function getFormattedPriceAttribute(): string
    {
        return number_format($this->price);
    }

    public function getFormattedNodesAttribute(): string
    {
        return number_format($this->nodes);
    }

    public function getFormattedSavingsAttribute(): ?string
    {
        return $this->savings ? number_format($this->savings) : null;
    }

    public function getPricePerNodeAttribute(): string
    {
        return number_format($this->price / $this->nodes, 2);
    }

    public function getDiscountPercentageAttribute(): ?int
    {
        if (!$this->is_discounted || !$this->original_price) {
            return null;
        }

        return round((($this->original_price - $this->price) / $this->original_price) * 100);
    }

    /**
     * Convert the package to an array format compatible with frontend
     */
    public function toFrontendArray(): array
    {
        $data = [
            'id' => (string) $this->id,
            'nodes' => $this->nodes,
            'nodesFormatted' => $this->formatted_nodes,
            'price' => $this->price,
            'priceFormatted' => $this->formatted_price,
            'pricePerNode' => $this->price_per_node,
        ];

        if ($this->savings) {
            $data['savings'] = $this->savings;
            $data['savingsFormatted'] = $this->formatted_savings;
        }

        if ($this->is_discounted) {
            $data['isDiscounted'] = true;
            $data['originalPrice'] = $this->original_price;
            $data['originalPriceFormatted'] = number_format($this->original_price);
            $data['discountPercentage'] = $this->discount_percentage;
        }

        return $data;
    }
}
