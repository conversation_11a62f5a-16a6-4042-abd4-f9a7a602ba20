<?php

namespace App\Models;

use App\Enums\RequestStatus;
use App\Observers\NodeAdditionObserver;
use App\Services\ProcessNode;
use App\Traits\BelongsToTenant;
use DB;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

#[ObservedBy(NodeAdditionObserver::class)]
class NodeAddition extends Model
{
    use BelongsToTenant;

    protected $appends = ['status_ar'];

    protected function casts(): array
    {
        return [
            'node_attributes' => 'collection',
            'status' => RequestStatus::class,
        ];
    }

    protected function statusAr(): Attribute
    {
        return Attribute::get(fn() => $this->status->translate());
    }

    public function parent(): BelongsTo
    {
        return $this->belongsTo(Node::class);
    }

    public function approve(): void
    {
        DB::transaction(function () {
            ProcessNode::handle([
                'parent_id' => $this->parent_id,
                ...$this->node_attributes->toArray(),
            ]);

            $this->update([
                'status' => RequestStatus::approved,
            ]);
        });
    }

    public function reject()
    {
        if (isset($this->node_attributes['photo'])) {
            Storage::delete($this->node_attributes['photo']);
        }

        $this->update([
            'status' => RequestStatus::rejected,
        ]);
    }

    public function member(): BelongsTo
    {
        return $this->belongsTo(Member::class);
    }
}
