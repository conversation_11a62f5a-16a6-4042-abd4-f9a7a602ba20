<?php

namespace App\Models;

use App\Traits\BelongsToTenant;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Line extends Model
{
    use BelongsToTenant;

    protected function casts(): array
    {
        return [
            'points' => 'array',
        ];
    }

    public function fromNode(): BelongsTo
    {
        return $this->belongsTo(Node::class);
    }

    public function toNode(): BelongsTo
    {
        return $this->belongsTo(Node::class);
    }
}
