<?php

namespace App\Models;

use App\Observers\RelationshipObserver;
use App\Traits\BelongsToTenant;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

#[ObservedBy([RelationshipObserver::class])]
class Relationship extends Model
{
    use BelongsToTenant;

    protected $guarded = ['id'];

    protected $appends = ['is_outside_family'];

    protected $with = ['wife', 'husband'];

    const MARRIAGE = 'marriage';

    const DIVORCE = 'divorce';

    const WIDOW = 'widow';

    public function isOutsideFamily(): Attribute
    {
        return Attribute::get(fn() => $this->name && $this->family_name);
    }

    public function husband(): BelongsTo
    {
        return $this->belongsTo(Node::class, 'husband_id');
    }

    public function wife(): BelongsTo
    {
        return $this->belongsTo(Node::class, 'wife_id');
    }

    public function otherParentRelationship(): BelongsTo
    {
        return $this->belongsTo(self::class, 'other_parent_relationship_id');
    }
}
