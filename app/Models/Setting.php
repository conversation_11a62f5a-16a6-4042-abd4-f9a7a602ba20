<?php

namespace App\Models;

use App\Enums\LayoutMode;
use App\Scopes\TenantScope;
use App\Traits\BelongsToTenant;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Storage;
use Str;
use function basename;
use function implode;
use const false;

class Setting extends Model
{
    use BelongsToTenant;

    const FAMILY_NAME = 'family_name';

    const FAMILY_DESCRIPTION = 'family_description';

    const URL = 'url';

    const DEFAULT_NODE_VISIBILITY = 'default_node_visibility';

    const FAMILY_LOGO = 'family_logo';

    const ABOUT = 'about';

    const HIDE_FEMALES_IN_PUBLIC_PAGE = 'hide_females_in_public_page';

    const MOBILE_APP_ENABLED = 'mobile_app_enabled';

    const SNAPCHAT_LINK = 'snapchat_link';

    const INSTAGRAM_LINK = 'instagram_link';

    const FACEBOOK_LINK = 'facebook_link';

    const TWITTER_LINK = 'twitter_link';

    const WHATSAPP_LINK = 'whatsapp_link';

    const YOUTUBE_LINK = 'youtube_link';

    const PAPER_TREE_URL = 'paper_tree_url';

    const ALLOW_GUESTS = 'allow_guests';

    const PASSWORD = 'password';

    const LAYOUT_MODE = 'layout_mode';

    const FOLDERS = [
        self::FAMILY_LOGO => 'family-logos',
        self::PAPER_TREE_URL => 'paper-trees',
    ];

    const ARRAY_SETTINGS = [self::DEFAULT_NODE_VISIBILITY];

    protected static function storeFile(string $key, UploadedFile|string|null $newValue): string|false
    {
        $oldValue = self::firstWhere('key', $key)->getRawOriginal('value');

        if (!$newValue || is_string($newValue)) {
            return $oldValue;
        }

        if ($oldValue) {
            Storage::delete($oldValue);
        }

        return $newValue->store(self::FOLDERS[$key]);
    }

    public static function isFile(string $key): bool
    {
        return in_array($key, array_keys(self::FOLDERS));
    }

    protected function value(): Attribute
    {
        return Attribute::make(
            get: function ($value, array $attributes) {
                if (self::isFile($attributes['key']) && $value) {
                    return Storage::temporaryUrl($value, now()->addDays(5));
                }

                return self::formatValue($value, $attributes['key'] !== self::PASSWORD);
            }
        );
    }

    public function scopeUrl($query, $url)
    {
        return $query->keyValue(self::URL, $url);
    }

    public function scopeKeyValue(Builder $query, string $key, $value): Builder
    {
        return $query->where('key', $key)->where('value', $value);
    }

    public static function set(string $key, $value): void
    {
        if ($key === self::URL) {
            $value = basename(Str::slug($value));
        }

        if (is_array($value)) {
            $value = implode(',', $value);
        }

        if (self::isFile($key)) {
            $value = self::storeFile($key, $value);
        }

        self::firstWhere('key', $key)->update(['value' => $value ?? '']);
    }

    public static function setMany(array $settings)
    {
        tenant()->forgetCache('settings');

        DB::transaction(fn() => collect($settings)->each(fn($value, $key) => self::set($key, $value)));
    }

    public static function removeFile(string $file): void
    {
        if (!self::isFile($file) || !($setting = self::firstWhere('key', $file))) {
            invalidate('الملف غير موجود');
        }

        Storage::delete($setting->getRawOriginal('value'));

        tenant()->forgetCache('settings');

        $setting->update(['value' => '']);
    }

    public static function isUrlTaken($url, ?Tenant $tenant = null): bool
    {
        return Setting::withoutGlobalScope(TenantScope::class)
            ->url($url)
            ->when($tenant, fn($q) => $q->where('tenant_id', '!=', $tenant->id))
            ->exists();
    }

    public static function setup(Tenant $tenant, string $familyName)
    {
        $tenant->settings()->createMany([
            ['key' => Setting::FAMILY_NAME, 'value' => $familyName],
            ['key' => Setting::FAMILY_DESCRIPTION, 'value' => ''],
            ['key' => Setting::URL, 'value' => strtolower(self::generateRandomUniqueUrl())],
            [
                'key' => Setting::DEFAULT_NODE_VISIBILITY,
                'value' => implode(',', NodeVisibility::DEFAULT_VISIBLE_ATTRIBUTES),
            ],
            ['key' => Setting::FAMILY_LOGO, 'value' => ''],
            ['key' => Setting::ABOUT, 'value' => ''],
            ['key' => Setting::HIDE_FEMALES_IN_PUBLIC_PAGE, 'value' => false],
            ['key' => Setting::MOBILE_APP_ENABLED, 'value' => true],
            ['key' => Setting::SNAPCHAT_LINK, 'value' => ''],
            ['key' => Setting::INSTAGRAM_LINK, 'value' => ''],
            ['key' => Setting::FACEBOOK_LINK, 'value' => ''],
            ['key' => Setting::TWITTER_LINK, 'value' => ''],
            ['key' => Setting::WHATSAPP_LINK, 'value' => ''],
            ['key' => Setting::YOUTUBE_LINK, 'value' => ''],
            ['key' => Setting::PAPER_TREE_URL, 'value' => ''],
            ['key' => Setting::ALLOW_GUESTS, 'value' => false],
            ['key' => Setting::PASSWORD, 'value' => ''],
            ['key' => Setting::LAYOUT_MODE, 'value' => LayoutMode::Auto->value],
        ]);
    }

    public static function generateRandomUniqueUrl(): string
    {
        $generateUrl = function (): string {
            $characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
            $letters = 'abcdefghijklmnopqrstuvwxyz';

            // Start with a letter for better readability
            $url = $letters[rand(0, 25)];

            // Add 9 more random characters (letters or numbers)
            for ($i = 1; $i < 10; $i++) {
                $url .= $characters[rand(0, 35)];
            }

            return $url;
        };

        do {
            $url = $generateUrl();
        } while (self::isUrlTaken($url));

        return $url;
    }

    public static function formatValue(?string $value, bool $castToNumber = true): bool|int|string|null|array
    {
        if ($value === null) {
            return null;
        }

        if ($value === '1' || $value === '0') {
            return (bool) $value;
        }

        if (is_numeric($value) && $castToNumber) {
            return (int) $value;
        }

        if (in_array($value, self::ARRAY_SETTINGS)) {
            return explode(',', $value);
        }

        return $value;
    }

    public static function addNewSettingToAllTenants(string $key, mixed $value): void
    {
        Tenant::each(fn(Tenant $tenant) => $tenant->settings()->firstOrCreate(['key' => $key], ['value' => $value]));
    }
}
