<?php

namespace App\Models;

use App\Traits\BelongsToTenant;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

class Guest extends Model
{
    use BelongsToTenant;
    use HasApiTokens;
    use HasFactory;
    use Notifiable; // we dont currently send notification but added for compatibility

    protected $appends = ['resource_name'];

    protected function resourceName(): Attribute
    {
        return Attribute::get(fn() => 'guest');
    }
}
