<?php

namespace App\Models;

use App\Enums\IdentifierType;
use App\Mail\OTPMail;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Mail;

class OneTimePassword extends Model
{
    use HasFactory;

    protected function casts(): array
    {
        return [
            'identifier_type' => IdentifierType::class,
        ];
    }

    public function scopeMobile(Builder $query, string $mobile)
    {
        // take what after 5, so we ignore if the input start with 09665, or 05, 9665.
        return $query->where('identifier', 'like', '%' . substr($mobile, -9));
    }

    public static function current(string $identifier, IdentifierType $type): ?OneTimePassword
    {
        return self::latest()
            ->where('identifier_type', $type)
            ->when(
                $type->mobile(),
                fn($query) => $query->mobile($identifier),
                fn($query) => $query->where('identifier', $identifier)
            )
            ->first();
    }

    public function isExpired()
    {
        return $this->created_at->addMinutes(2)->isPast();
    }

    public function check($attemptedPassword)
    {
        return (int) $this->password === (int) $attemptedPassword;
    }

    public static function ensureOtpIsValid(string $identifier, IdentifierType $type, string $rawOtp): void
    {
        $otp = OneTimePassword::current($identifier, $type);

        if ($otp->isExpired()) {
            OneTimePassword::sendTo($identifier, $type);

            invalidate([
                'otp' => 'الرقم انتهت صلاحيته. أرسلنا رقما جديدا',
            ]);
        }

        if (!$otp->check($rawOtp)) {
            invalidate(['otp' => 'الرقم غير صحيح']);
        }
    }

    public static function generatePassword(string $identifier): int
    {
        return is_testing($identifier) ? 1234 : random_int(1111, 9999);
    }

    public static function sendTo(string $identifier, IdentifierType $type): ?OneTimePassword
    {
        $otp = self::current($identifier, $type);

        if ($otp && !$otp->isExpired()) {
            return $otp;
        }

        $password = self::generatePassword($identifier);

        $otp = OneTimePassword::create([
            'identifier' => $identifier,
            'identifier_type' => $type,
            'password' => $password,
        ]);

        if ($type->mobile()) {
            SMSMessage::sendTo($identifier, 'رقم التحقق: ' . $password);
        }

        if ($type->email()) {
            Mail::to($identifier)->send(new OTPMail($otp));
        }

        return $otp;
    }
}
