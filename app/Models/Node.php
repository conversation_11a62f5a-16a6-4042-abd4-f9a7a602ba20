<?php

namespace App\Models;

use App\Enums\Gender;
use App\Enums\LifeStatus;
use App\Observers\NodeObserver;
use App\Traits\BelongsToTenant;
use GeniusTS\HijriDate\Hijri;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Facades\DB;
use Staudenmeir\LaravelAdjacencyList\Eloquent\HasRecursiveRelationships;
use Storage;
use function request;
use function tenant;

#[ObservedBy([NodeObserver::class])]
class Node extends Model
{
    use BelongsToTenant;
    use HasFactory;
    use HasRecursiveRelationships;

    protected $hidden = ['tenant_id'];

    public const EDITABLE_COLUMNS = [
        'name',
        'life_status',
        'birth_date',
        'death_date',
        'mobile',
        'email',
        'gender',
        'parent_id',
        'about',
        'nickname',
        'country_id',
        'city_id',
        'district_id',
        'label',
        'bg_color',
        'size',
        'order',
        'photo',
        'style',
        'added_to_paper_at',
        'relationships',
        'other_parent_relationship_id',
    ];

    protected $appends = ['photo_url', 'life_status_ar'];

    protected function casts(): array
    {
        return [
            'is_root' => 'boolean',
            'birth_date' => 'date:Y-m-d',
            'death_date' => 'date:Y-m-d',
            'added_to_paper_at' => 'date:Y-m-d',
            'style' => 'array',
            'life_status' => LifeStatus::class,
            'gender' => Gender::class,
        ];
    }

    protected function lifeStatusAr(): Attribute
    {
        return Attribute::get(fn() => $this->life_status?->translate());
    }

    public function scopeSelectBasicInfo(Builder $query): Builder
    {
        return $query->select([
            'id',
            'name',
            'life_status',
            'is_root',
            'gender',
            'parent_id',
            'style',
            'bg_color',
            'other_parent_relationship_id',
        ]);
    }

    public function scopeStyled(Builder $query): Builder
    {
        return $query->where(fn(Builder $query) => $query->whereNotNull('style')->orWhere('is_root', true));
    }

    public function scopeApplyTenantSettings(Builder $query): Builder
    {
        $tenant = tenant();

        return $query
            ->select(NodeVisibility::columnsOf($tenant))
            ->when($tenant->getSetting(Setting::HIDE_FEMALES_IN_PUBLIC_PAGE), fn($q) => $q->male());
    }

    public function scopeMobile(Builder $query, string $mobile)
    {
        // take what after 5, so we ignore if the input start with 09665, or 05, 9665.
        return $query->where('mobile', 'like', '%' . substr($mobile, -9));
    }

    public function getHijriBirthDateAttribute()
    {
        return $this->birth_date ? Hijri::convertToHijri($this->birth_date)->format('Y/m/d') : null;
    }

    public function getHijriDeathDateAttribute()
    {
        return $this->death_date ? Hijri::convertToHijri($this->death_date)->format('Y/m/d') : null;
    }

    public function sonOfOrDaughtersOf()
    {
        return $this->gender === Gender::Male ? ' بن ' : ' بنت ';
    }

    public function photoUrl(): Attribute
    {
        return Attribute::get(fn() => $this->photo ? Storage::temporaryUrl($this->photo, now()->addDays(5)) : null);
    }

    public function scopeRoot(Builder $query): Builder
    {
        return $query->where('is_root', true);
    }

    public function scopeFilterLocationByRequest(Builder $query)
    {
        return $query
            ->when(request('country_id'), fn($query) => $query->where('country_id', request('country_id')))
            ->when(request('city_id'), fn($query) => $query->where('city_id', request('city_id')))
            ->when(request('district_id'), fn($query) => $query->where('district_id', request('district_id')));
    }

    public function scopeFilterByGender(Builder $query)
    {
        return $query
            ->when(request('gender') === Gender::Male->value, fn($query) => $query->male())
            ->when(request('gender') === Gender::Female->value, fn($query) => $query->female());
    }

    public function scopeSearch(Builder $query, string $text): Builder
    {
        $text = preg_replace(
            [
                '/[ًٌٍَُِّْ]/u', // Harakat
                '/[آأإ]/u', // Hamza variations
                '/[ةه]/u', // Ta Marbuta and Ha
                '/[يى]/u', // Ya and Alef Maksura
            ],
            [
                '', // Remove harakat
                'ا', // Normalize Hamza
                'ه', // Normalize Ta Marbuta and Ha
                'ي', // Normalize Ya and Alef Maksura
            ],
            $text
        );

        return $query
            ->whereFuzzy('normalized_full_name', $text)
            ->orWhereFuzzy('mobile', $text)
            ->orWhereFuzzy('id', $text);
    }

    public function scopeMale($query)
    {
        return $query->where('gender', Gender::Male);
    }

    public function scopeFemale($query)
    {
        return $query->where('gender', Gender::Female);
    }

    public function scopeAlive($query)
    {
        return $query->where('life_status', LifeStatus::alive);
    }

    public function scopeDead($query)
    {
        return $query->where('life_status', LifeStatus::dead);
    }

    public function scopeUnknownLifeStatus($query)
    {
        return $query->where('life_status', LifeStatus::unknown);
    }

    public function scopeMostCommonNames($query)
    {
        return $query
            ->select('name')
            ->selectRaw('COUNT(*) AS count')
            ->groupBy('name')
            ->orderByDesc('count')
            ->having('count', '>', 1)
            ->limit(10);
    }

    public function scopeMostCities($query)
    {
        return $query
            ->select('city_id')
            ->whereNotNull('city_id')
            ->selectRaw('COUNT(*) AS count')
            ->groupBy('city_id')
            ->orderByDesc('count')
            ->having('count', '>=', 1)
            ->limit(10);
    }

    public function scopeMostChildrenNodes($query)
    {
        return $query
            ->with('parent.parent')
            ->withCount('children')
            ->orderByDesc('children_count')
            ->having('children_count', '>', 1)
            ->limit(10);
    }

    public function delete()
    {
        return DB::transaction(function () {
            $this->visibility()->delete();
            $this->member()->delete();

            $this->nodeAdditions()->delete();
            $this->nodeChanges()->delete();

            $this->users()->detach();

            Line::whereIn('to_node_id', [$this->id, ...$this->descendants()->pluck('id')])->delete();

            $this->descendants->each->delete();

            return parent::delete();
        });
    }

    public function nodeAdditions(): HasMany
    {
        return $this->hasMany(NodeAddition::class, 'parent_id');
    }

    public function nodeChanges(): HasMany
    {
        return $this->hasMany(NodeChange::class, 'node_id');
    }

    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'nodes_users');
    }

    public function visibility(): HasOne
    {
        return $this->hasOne(NodeVisibility::class);
    }

    public function loadRelationships(): Node
    {
        return $this->gender === Gender::Male ? $this->load('wives') : $this->load('husbands');
    }

    public function getRelationships()
    {
        return $this->gender === Gender::Male ? $this->wives : $this->husbands;
    }

    public function wives(): HasMany
    {
        return $this->hasMany(Relationship::class, 'husband_id');
    }

    public function husbands(): HasMany
    {
        return $this->hasMany(Relationship::class, 'wife_id');
    }

    public function sons()
    {
        return $this->children()->male();
    }

    public function daughters()
    {
        return $this->children()->female();
    }

    public function brothers()
    {
        return $this->siblings()->male();
    }

    public function sisters()
    {
        return $this->siblings()->female();
    }

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    public function city(): BelongsTo
    {
        return $this->belongsTo(City::class);
    }

    public function district(): BelongsTo
    {
        return $this->belongsTo(District::class);
    }

    public function member(): HasOne
    {
        return $this->hasOne(Member::class);
    }

    public function updateFullName()
    {
        $fullName = $this->is_root
            ? $this->name
            : $this->name .
                $this->sonOfOrDaughtersOf() .
                $this->ancestors
                    ->map(function ($ancestor, $index) {
                        $sonOfOrDaughtersOf =
                            $index < $this->ancestors->count() - 1 ? $ancestor->sonOfOrDaughtersOf() : '';

                        return $ancestor->name . $sonOfOrDaughtersOf;
                    })
                    ->implode(' ');

        $this->update([
            'full_name' => preg_replace('/\s+/', ' ', $fullName),
        ]);

        return $this;
    }

    public function otherParentRelationship(): BelongsTo
    {
        return $this->belongsTo(Relationship::class, 'other_parent_relationship_id');
    }
}
