<?php

namespace App\Models;

use App\Traits\BelongsToTenant;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Storage;

class Post extends Model
{
    use BelongsToTenant;

    protected $appends = ['image_url'];

    protected function casts(): array
    {
        return [
            'published_at' => 'datetime',
            'start_at' => 'datetime',
            'end_at' => 'datetime',
            'links' => 'array',
        ];
    }

    protected function imageUrl(): Attribute
    {
        return Attribute::get(fn() => $this->image ? Storage::temporaryUrl($this->image, now()->addDays(5)) : null);
    }
}
