<?php

namespace App\Models;

use App\Enums\Gender;
use App\Enums\LifeStatus;
use App\Traits\BelongsToTenant;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Branch extends Model
{
    use BelongsToTenant;
    use HasFactory;

    protected $hidden = ['tenant_id'];

    public static function setup(Tenant $tenant, string $familyName)
    {
        return $tenant->branches()->create(['name' => $familyName]);
    }

    public function createRoot(string $name): Node
    {
        return $this->nodes()->create([
            'is_root' => true,
            'name' => $name,
            'full_name' => $name,
            'gender' => Gender::Male,
            'life_status' => LifeStatus::unknown,
            'tenant_id' => $this->tenant_id,
            'bg_color' => Gender::Male->color(),
        ]);
    }

    public function getRoot(?array $columns = null): Node
    {
        return $this->nodes()
            ->select($columns ?? '*')
            ->firstWhere('is_root', true);
    }

    public function nodes(): HasMany
    {
        return $this->hasMany(Node::class);
    }

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'branches_users');
    }

    public function scopeUser($query, User $user)
    {
        return $query->whereHas('users', fn($query) => $query->where('user_id', $user->id));
    }
}
