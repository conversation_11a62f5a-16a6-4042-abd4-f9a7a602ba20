<?php

namespace App\Models;

use App\Traits\BelongsToTenant;
use Illuminate\Database\Eloquent\Model;

class NodeVisibility extends Model
{
    use BelongsToTenant;

    const NODE_ATTRIBUTES = [
        'name',
        'life_status',
        'birth_date',
        'death_date',
        'mobile',
        'gender',
        'about',
        'full_name',
        'nickname',
    ];

    const DEFAULT_VISIBLE_ATTRIBUTES = ['name', 'full_name', 'life_status', 'gender', 'about'];

    const RELATIONSHIPS = [];

    protected function casts(): array
    {
        return [
            'hidden_attributes' => 'collection',
        ];
    }

    public static function columnsOf(Tenant $tenant)
    {
        return [
            'id',
            'parent_id',
            'x',
            'y',
            'bg_color',
            'size',
            'photo',
            'is_root',
            ...array_diff(explode(',', $tenant->getSetting(Setting::DEFAULT_NODE_VISIBILITY)), self::RELATIONSHIPS),
        ];
    }
}
