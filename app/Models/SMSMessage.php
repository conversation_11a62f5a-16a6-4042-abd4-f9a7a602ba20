<?php

namespace App\Models;

use App\Jobs\SendSmsJob;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

use function now;

class SMSMessage extends Model
{
    use HasFactory;

    protected function casts(): array
    {
        return [
            'sent_at' => 'datetime',
        ];
    }

    public static function sendTo(string $mobile, string $messageText)
    {
        $sms = SMSMessage::create([
            'body' => $messageText,
            'mobile' => $mobile,
        ]);

        if (is_testing($mobile)) {
            $sms->update(['sent_at' => now()]);

            return;
        }

        SendSmsJob::dispatch($sms);
    }
}
