<?php

namespace App\Models;

use App\Enums\IdentifierType;
use App\Traits\BelongsToTenant;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use NotificationChannels\Expo\ExpoPushToken;

class Member extends Authenticatable
{
    use BelongsToTenant;
    use HasApiTokens;
    use Notifiable;
    use HasFactory;

    protected $appends = ['resource_name'];

    protected function casts(): array
    {
        return [
            'expo_token' => ExpoPushToken::class,
        ];
    }

    protected function resourceName(): Attribute
    {
        return Attribute::get(fn() => 'member');
    }

    public function node(): BelongsTo
    {
        return $this->belongsTo(Node::class);
    }

    public function nodeAddition(): HasOne
    {
        return $this->hasOne(NodeAddition::class);
    }

    public function nodeChanges(): HasMany
    {
        return $this->hasMany(NodeChange::class);
    }

    /**
     * Route notifications for the Expo channel.
     *
     * @return ExpoPushToken|null
     */
    public function routeNotificationForExpo(): ?ExpoPushToken
    {
        return $this->expo_token;
    }

    public static function firstOrCreateByIdentifier(
        string $identifier,
        IdentifierType $type,
        ?Tenant $tenant = null
    ): ?Member {
        $nodeQuery = $tenant ? $tenant->nodes() : Node::query();

        $node = $nodeQuery
            ->when(
                $type->mobile(),
                fn($query) => $query->mobile($identifier),
                fn($query) => $query->where('email', $identifier)
            )
            ->first();

        return $node ? $node->member()->firstOrCreate(['tenant_id' => $node->tenant_id]) : null;
    }
}
