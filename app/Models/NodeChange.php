<?php

namespace App\Models;

use App\Enums\RequestStatus;
use App\Observers\NodeChangeObserver;
use App\Services\ProcessNode;
use App\Traits\BelongsToTenant;
use DB;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

#[ObservedBy(NodeChangeObserver::class)]
class NodeChange extends Model
{
    use BelongsToTenant;

    protected $appends = ['status_ar'];

    protected function casts(): array
    {
        return [
            'new_attributes' => 'array',
            'old_attributes' => 'array',
            'status' => RequestStatus::class,
        ];
    }

    protected function statusAr(): Attribute
    {
        return Attribute::get(fn() => $this->status->translate());
    }

    public function node(): BelongsTo
    {
        return $this->belongsTo(Node::class);
    }

    public function approve(): void
    {
        DB::transaction(function () {
            ProcessNode::handle($this->new_attributes, $this->node);

            $this->update([
                'status' => RequestStatus::approved,
            ]);
        });
    }

    public function reject(): void
    {
        if (
            isset($this->new_attributes['photo']) &&
            $this->new_attributes['photo'] !== $this->node->photo // just to make sure we are not deleting the node photo
        ) {
            Storage::delete($this->new_attributes['photo']);
        }

        $this->update([
            'status' => RequestStatus::rejected,
        ]);
    }

    public function member(): BelongsTo
    {
        return $this->belongsTo(Member::class);
    }
}
