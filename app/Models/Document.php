<?php

namespace App\Models;

use App\Traits\BelongsToTenant;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

class Document extends Model
{
    use HasFactory;
    use BelongsToTenant;

    protected $guarded = ['id'];

    protected function casts(): array
    {
        return [
            'compression_metadata' => 'array',
        ];
    }

    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    public function getFileUrlAttribute(): string
    {
        return Storage::url($this->file_path);
    }

    public function getCompressedFileUrlAttribute(): ?string
    {
        return $this->compressed_file_path ? Storage::url($this->compressed_file_path) : null;
    }

    public function getFormattedFileSizeAttribute(): string
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    public function getFileExtensionAttribute(): string
    {
        return pathinfo($this->original_filename, PATHINFO_EXTENSION);
    }

    public function isPdf(): bool
    {
        return $this->mime_type === 'application/pdf';
    }

    public function isImage(): bool
    {
        return str_starts_with($this->mime_type, 'image/');
    }

    public function isCompressed(): bool
    {
        return $this->compression_status === 'completed';
    }

    public function isCompressionPending(): bool
    {
        return $this->compression_status === 'pending';
    }

    public function isCompressionProcessing(): bool
    {
        return $this->compression_status === 'processing';
    }

    public function isCompressionFailed(): bool
    {
        return $this->compression_status === 'failed';
    }

    public function markCompressionAsProcessing(): void
    {
        $this->update(['compression_status' => 'processing']);
    }

    public function markCompressionAsCompleted(string $compressedFilePath, array $metadata = []): void
    {
        $this->update([
            'compression_status' => 'completed',
            'compressed_file_path' => $compressedFilePath,
            'compression_metadata' => $metadata,
        ]);
    }

    public function markCompressionAsFailed(array $metadata = []): void
    {
        $this->update([
            'compression_status' => 'failed',
            'compression_metadata' => $metadata,
        ]);
    }

    public function scopePendingCompression($query)
    {
        return $query->where('compression_status', 'pending')->where('mime_type', 'application/pdf');
    }

    public function scopeCompressed($query)
    {
        return $query->where('compression_status', 'completed');
    }
}
