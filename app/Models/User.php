<?php

namespace App\Models;

use App\Enums\IdentifierType;
use App\Traits\BelongsToTenant;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use NotificationChannels\Expo\ExpoPushToken;

class User extends Authenticatable
{
    use BelongsToTenant;
    use HasApiTokens;
    use HasFactory;
    use Notifiable;

    protected $appends = ['resource_name', 'is_demo'];

    protected $with = ['tenant'];

    protected array $guard_name = ['web', 'sanctum'];

    protected function casts(): array
    {
        return [
            'expo_token' => ExpoPushToken::class,
        ];
    }

    protected function resourceName(): Attribute
    {
        return Attribute::get(fn() => 'user');
    }

    protected function isDemo(): Attribute
    {
        return Attribute::get(fn() => $this->mobile === config('app.demo_mobile'));
    }

    public static function findDemo(): self
    {
        return self::firstWhere('mobile', config('app.demo_mobile'));
    }

    /**
     * Route notifications for the Expo channel.
     *
     * @return ExpoPushToken|null
     */
    public function routeNotificationForExpo(): ?ExpoPushToken
    {
        return $this->expo_token;
    }

    public function scopeMobile(Builder $query, string $mobile)
    {
        // take what after 5, so we ignore if the input start with 09665, or 05, 9665.
        return $query->where('mobile', 'like', '%' . substr($mobile, -9));
    }

    public static function byIdentifier(string $identifier, IdentifierType $type, ?Tenant $tenant = null): ?User
    {
        $query = $tenant ? $tenant->users() : User::query();

        return $query
            ->when(
                $type->mobile(),
                fn($query) => $query->mobile($identifier),
                fn($query) => $query->where('email', $identifier)
            )
            ->first();
    }

    public static function setupOwner(Tenant $tenant, string $name, string $mobile): self
    {
        return $tenant->users()->create([
            'name' => $name,
            'mobile' => $mobile,
            'is_owner' => true,
        ]);
    }

    public function messages(): MorphMany
    {
        return $this->morphMany(SMSMessage::class, 'sendable');
    }

    public function branches(): BelongsToMany
    {
        return $this->belongsToMany(Branch::class, 'branches_users');
    }

    public function nodes(): BelongsToMany
    {
        return $this->belongsToMany(Node::class, 'nodes_users');
    }

    public function getMainNode()
    {
        return $this->is_owner ? tenant()->getMainBranch()->getRoot() : $this->nodes()->first();
    }
}
