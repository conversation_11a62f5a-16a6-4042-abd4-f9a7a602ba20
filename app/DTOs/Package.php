<?php

namespace App\DTOs;

class Package
{
    public string $id;
    public int $nodes;
    public int $price;
    public ?int $savings = null;
    public bool $isDiscounted = false;
    public ?int $originalPrice = null;

    public function __construct(array $data)
    {
        $this->id = $data['id'];
        $this->nodes = $data['nodes'];
        $this->price = $data['price'];

        if (isset($data['savings'])) {
            $this->savings = $data['savings'];
        }

        if (isset($data['isDiscounted']) && $data['isDiscounted']) {
            $this->isDiscounted = true;
            $this->originalPrice = $data['originalPrice'] ?? null;
        }
    }

    /**
     * Get all available packages
     *
     * @return array<string, Package>
     */
    public static function all(): array
    {
        return [
            '100' => new self([
                'id' => '100',
                'nodes' => 100,
                'price' => 150,
            ]),
            '500' => new self([
                'id' => '500',
                'nodes' => 500,
                'price' => 600,
                'savings' => 150,
            ]),
            '1000' => new self([
                'id' => '1000',
                'nodes' => 1000,
                'price' => 1000,
                'savings' => 500,
            ]),
            '5000' => new self([
                'id' => '5000',
                'nodes' => 5000,
                'price' => 4000,
                'savings' => 3500,
            ]),
            '50' => new self([
                'id' => '50',
                'nodes' => 50,
                'price' => 64,
                'isDiscounted' => true,
                'originalPrice' => 75,
            ]),
        ];
    }

    /**
     * Find a package by ID
     *
     * @param string $id
     * @return Package|null
     */
    public static function find(string $id): ?Package
    {
        return self::all()[$id] ?? null;
    }

    /**
     * Convert the package to an array
     *
     * @return array
     */
    public function toArray(): array
    {
        $data = [
            'id' => $this->id,
            'nodes' => $this->nodes,
            'price' => $this->price,
        ];

        if ($this->savings !== null) {
            $data['savings'] = $this->savings;
        }

        if ($this->isDiscounted) {
            $data['isDiscounted'] = true;
            $data['originalPrice'] = $this->originalPrice;
        }

        return $data;
    }
}
