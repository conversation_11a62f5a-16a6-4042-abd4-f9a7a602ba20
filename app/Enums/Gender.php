<?php

namespace App\Enums;

enum Gender: string
{
    case Male = 'male';
    case Female = 'female';

    public function color(): string
    {
        return match ($this) {
            self::Male => '#3b82f6',
            self::Female => '#ec4899',
        };
    }

    public function translate(): string
    {
        return match ($this) {
            self::Male => 'ذكر',
            self::Female => 'أنثى',
        };
    }
}
