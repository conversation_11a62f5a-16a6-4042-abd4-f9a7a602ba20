<?php

namespace App\Enums;

enum RequestStatus: string
{
    case pending = 'pending';
    case approved = 'approved';
    case rejected = 'rejected';

    public function translate()
    {
        return match ($this) {
            self::pending => 'قيد الانتظار',
            self::approved => 'تمت الموافقة',
            self::rejected => 'تم الرفض',
        };
    }

    public static function values()
    {
        return [self::pending->value, self::approved->value, self::rejected->value];
    }
}
