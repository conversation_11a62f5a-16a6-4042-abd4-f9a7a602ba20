<?php

namespace App\Services;

use App\Models\Setting;
use App\Models\Tenant;
use DB;

class CreateTenant
{
    public static function handle($data)
    {
        return DB::transaction(function () use ($data) {
            session()->forget('tenant_id');

            $tenant = Tenant::create([
                'is_eligible_for_discount' => true,
                'max_nodes_number' => config('app.default_max_nodes_count'),
            ]);

            session(['tenant_id' => $tenant->id]);

            Setting::setup($tenant, $data['family_name']);

            $tenant->stat()->create([
                'most_common_full_names' => [
                    ['length' => 3, 'names' => []],
                    ['length' => 4, 'names' => []],
                    ['length' => 5, 'names' => []],
                    ['length' => 6, 'names' => []],
                ],
                'number_of_nodes_in_each_generation' => [],
            ]);

            $tenant->users()->create([
                'name' => $data['name'],
                $data['identifier_type'] => $data['identifier'],
                'is_owner' => true,
            ]);

            $branch = $tenant->branches()->create(['name' => $data['family_name']]);

            $branch->createRoot($data['root_name']);

            return $tenant;
        });
    }
}
