<?php

namespace App\Services;

use Ilovepdf\Ilovepdf;
use Ilovepdf\CompressTask;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ILovePdfService
{
    private Ilovepdf $ilovepdf;

    public function __construct()
    {
        $apiKey = config('services.ilove_pdf.api_key');
        
        if (!$apiKey) {
            throw new \Exception('iLovePDF API key not configured');
        }

        $this->ilovepdf = new Ilovepdf($apiKey, true); // true for public key
    }

    /**
     * Compress a PDF file and return the compressed content
     *
     * @param string $filePath Path to the file in storage
     * @param string $compressionLevel 'low', 'recommended', or 'extreme'
     * @return string Compressed file content
     * @throws \Exception
     */
    public function compressPdf(string $filePath, string $compressionLevel = 'recommended'): string
    {
        if (!Storage::exists($filePath)) {
            throw new \Exception("File not found: {$filePath}");
        }

        // Validate compression level
        $validLevels = ['low', 'recommended', 'extreme'];
        if (!in_array($compressionLevel, $validLevels)) {
            throw new \Exception("Invalid compression level. Must be one of: " . implode(', ', $validLevels));
        }

        try {
            // Create a new compress task
            $compressTask = $this->ilovepdf->newTask('compress');

            // Get file content and create a temporary file
            $fileContent = Storage::get($filePath);
            $tempFilePath = $this->createTempFile($fileContent, $filePath);

            try {
                // Add file to the task
                $file = $compressTask->addFile($tempFilePath);

                // Set compression level
                $compressTask->setCompressionLevel($compressionLevel);

                // Execute the task
                $compressTask->execute();

                // Download the compressed file
                $compressedContent = $compressTask->download();

                Log::info('PDF compression completed successfully', [
                    'file_path' => $filePath,
                    'compression_level' => $compressionLevel,
                    'original_size' => strlen($fileContent),
                    'compressed_size' => strlen($compressedContent),
                    'compression_ratio' => round((1 - strlen($compressedContent) / strlen($fileContent)) * 100, 2),
                ]);

                return $compressedContent;

            } finally {
                // Clean up temporary file
                if (file_exists($tempFilePath)) {
                    unlink($tempFilePath);
                }
            }

        } catch (\Exception $e) {
            Log::error('iLovePDF compression failed', [
                'file_path' => $filePath,
                'compression_level' => $compressionLevel,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('PDF compression failed: ' . $e->getMessage());
        }
    }

    /**
     * Create a temporary file from content
     *
     * @param string $content File content
     * @param string $originalPath Original file path for extension
     * @return string Temporary file path
     */
    private function createTempFile(string $content, string $originalPath): string
    {
        $extension = pathinfo($originalPath, PATHINFO_EXTENSION);
        $tempFilePath = tempnam(sys_get_temp_dir(), 'ilovepdf_') . '.' . $extension;
        
        if (file_put_contents($tempFilePath, $content) === false) {
            throw new \Exception('Failed to create temporary file');
        }

        return $tempFilePath;
    }

    /**
     * Check if the service is properly configured
     *
     * @return bool
     */
    public function isConfigured(): bool
    {
        return !empty(config('services.ilove_pdf.api_key'));
    }

    /**
     * Get available compression levels
     *
     * @return array
     */
    public function getCompressionLevels(): array
    {
        return [
            'low' => 'Low compression (larger file size, better quality)',
            'recommended' => 'Recommended compression (balanced)',
            'extreme' => 'Extreme compression (smaller file size, lower quality)',
        ];
    }
}
