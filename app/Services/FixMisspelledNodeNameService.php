<?php

namespace App\Services;

class FixMisspelledNodeNameService
{
    const NAMES = [
        'ابراهيم' => 'إبراهيم',
        'أبراهيم' => 'إبراهيم',
        'براهيم' => 'إبراهيم',
        'احمد' => 'أحمد',
        'عبدالرحمن' => 'عبد الرحمن',
        'عبدالله' => 'عبد الله',
        'عبدالعزيز' => 'عبد العزيز',
        'عبدالإله' => 'عبد الإله',
        'عبدالمحسن' => 'عبد المحسن',
        'عبدالسلام' => 'عبد السلام',
        'عبداللطيف' => 'عبد اللطيف',
        'عبدالكريم' => 'عبد الكريم',
        'نوره' => 'نورة',
        'لولوه' => 'لولوة',
        'ساره' => 'سارة',
        'هيله' => 'هيلة',
        'الجوهره' => 'الجوهرة',
        'اسامه' => 'أسامة',
        'أسامه' => 'أسامة',
        'اسامة' => 'أسامة',
        'حصه' => 'حصة',
        'فاطمه' => 'فاطمة',
        'امل' => 'أمل',
        'عائشه' => 'عائشة',
        'رقيه' => 'رقية',
        'لطيفه' => 'لطيفة',
        'اسماء' => 'أسماء',
    ];

    public static function handle(string $name)
    {
        return self::NAMES[trim($name)] ?? $name;
    }
}
