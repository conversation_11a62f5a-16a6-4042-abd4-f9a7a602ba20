<?php

namespace App\Services;

use App\Models\Node;

class QuickStatisticsQueryService
{
    public function handle(): array
    {
        $nodeQuery = Node::query();

        return [
            'maleNumber' => $nodeQuery->clone()->male()->count(),
            'femaleNumber' => $nodeQuery->clone()->female()->count(),
            'mostCommonNames' => $nodeQuery->clone()->mostCommonNames()->take(3)->get('name'),
        ];
    }
}
