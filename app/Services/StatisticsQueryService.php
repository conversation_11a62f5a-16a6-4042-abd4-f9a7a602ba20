<?php

namespace App\Services;

use App\Enums\Gender;
use App\Models\Node;
use Illuminate\Validation\Rule;

use function now;
use function request;
use function tenant;

class StatisticsQueryService
{
    public function handle(): array
    {
        request()->validate([
            'node_id' => ['nullable', 'exists:nodes,id'],
            'label' => ['nullable', 'string'],
            'country_id' => ['nullable', 'exists:countries,id'],
            'city_id' => ['nullable', 'exists:cities,id'],
            'district_id' => ['nullable', 'exists:districts,id'],
            'gender' => ['nullable', Rule::enum(Gender::class)],
        ]);

        $currentNode =
            request('node_id') || request('label')
                ? (request('label')
                    ? Node::firstWhere('label', request('label'))
                    : Node::find(request('node_id')))
                : null;

        $nodeQuery = $currentNode ? $currentNode->descendantsAndSelf() : Node::query();

        return [
            'currentNode' => $currentNode,
            'stat' => tenant()->stat,
            'aliveNodeNumber' => $nodeQuery->clone()->filterLocationByRequest()->filterByGender()->alive()->count(),
            'deadNodeNumber' => $nodeQuery->clone()->filterLocationByRequest()->filterByGender()->dead()->count(),
            'aliveMaleNumber' => $nodeQuery->clone()->filterLocationByRequest()->male()->alive()->count(),
            'deadMaleNumber' => $nodeQuery->clone()->filterLocationByRequest()->male()->dead()->count(),
            'aliveFemaleNumber' => $nodeQuery->clone()->filterLocationByRequest()->female()->alive()->count(),
            'deadFemaleNumber' => $nodeQuery->clone()->filterLocationByRequest()->female()->dead()->count(),
            'lastYearBirthNumber' => $nodeQuery
                ->clone()
                ->filterLocationByRequest()
                ->filterByGender()
                ->whereYear('birth_date', now()->year)
                ->count(),
            'lastYearMaleBirthNumber' => $nodeQuery
                ->clone()
                ->filterLocationByRequest()
                ->male()
                ->whereYear('birth_date', now()->year)
                ->count(),
            'lastYearFemaleBirthNumber' => $nodeQuery
                ->clone()
                ->filterLocationByRequest()
                ->female()
                ->whereYear('birth_date', now()->year)
                ->count(),
            'mostCommonNames' => $nodeQuery
                ->clone()
                ->filterLocationByRequest()
                ->mostCommonNames()
                ->filterByGender()
                ->get('name'),
            'mostCommonMaleNames' => $nodeQuery
                ->clone()
                ->filterLocationByRequest()
                ->mostCommonNames()
                ->male()
                ->get('name'),
            'mostCommonFemaleNames' => $nodeQuery
                ->clone()
                ->filterLocationByRequest()
                ->mostCommonNames()
                ->female()
                ->get('name'),
            'mostChildrenNodes' => $nodeQuery
                ->clone()
                ->filterLocationByRequest()
                ->mostChildrenNodes()
                ->filterByGender()
                ->get('name'),
            'mostCities' => $nodeQuery->clone()->mostCities()->filterByGender()->get('name')->map(
                fn($node) => [
                    'name' => $node->city->name,
                    'count' => $node->count,
                ]
            ),
        ];
    }
}
