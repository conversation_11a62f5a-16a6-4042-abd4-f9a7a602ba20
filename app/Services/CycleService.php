<?php

namespace App\Services;

class CycleService
{
    public static function detectCycles(array $parentMap): array
    {
        // Build adjacency: parent_id => [child1, child2, ...]
        $adjacency = [];
        foreach ($parentMap as $child => $parent) {
            if ($parent !== null && isset($parentMap[$parent])) {
                $adjacency[$parent][] = $child;
            }
        }

        unset($parentMap); // free memory

        $visited = [];
        $stack = [];
        $cycles = [];

        foreach (array_keys($adjacency) as $node) {
            if (!isset($visited[$node])) {
                $path = [];
                if (self::dfs($node, $adjacency, $visited, $stack, $path)) {
                    $cycles[] = $path;
                }
            }
        }

        return $cycles;
    }

    /**
     * Recursive DFS to detect a cycle.
     *
     * @param int $current
     * @param array<int, int[]> &$adjacency
     * @param array<int, bool>  &$visited
     * @param array<int, bool>  &$stack
     * @param array<int>        &$path
     * @return bool
     */
    private static function dfs(int $current, array &$adjacency, array &$visited, array &$stack, array &$path): bool
    {
        $visited[$current] = true;
        $stack[$current] = true;
        $path[] = $current;

        foreach ($adjacency[$current] ?? [] as $child) {
            if (!isset($visited[$child])) {
                if (self::dfs($child, $adjacency, $visited, $stack, $path)) {
                    return true;
                }
            } elseif (!empty($stack[$child])) {
                $path[] = $child;
                return true;
            }
        }

        unset($stack[$current]);
        array_pop($path);

        return false;
    }
}
