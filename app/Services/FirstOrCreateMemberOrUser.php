<?php

namespace App\Services;

use App\Enums\IdentifierType;
use App\Models\Member;
use App\Models\User;

class FirstOrCreateMemberOrUser
{
    public function handle(string $identifier, string $identifierType, ?string $url = null): User|Member|null
    {
        $identifierType = IdentifierType::from($identifierType);

        $tenant = tenant($url);

        return Member::firstOrCreateByIdentifier($identifier, $identifierType, $tenant) ??
            User::byIdentifier($identifier, $identifierType, $tenant);
    }
}
