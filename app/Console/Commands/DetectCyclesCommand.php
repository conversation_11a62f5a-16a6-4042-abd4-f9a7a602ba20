<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Models\Node;
use App\Models\Tenant;
use App\Services\CycleService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Sentry;

class DetectCyclesCommand extends Command
{
    protected $signature = 'tree:detect-cycles';
    protected $description = 'Detect and fix cyclic relationships for all tenants.';

    public function handle(): int
    {
        $cyclesFound = [];
        $fixesApplied = [];

        Tenant::cursor()->each(function (Tenant $tenant) use (&$cyclesFound, &$fixesApplied): void {
            DB::transaction(function () use ($tenant, &$cyclesFound, &$fixesApplied) {
                // Load only ID and parent_id into a simple array
                $parentMap = Node::where('tenant_id', $tenant->id)->pluck('parent_id', 'id')->all();

                $cycles = CycleService::detectCycles($parentMap);

                foreach ($cycles as $path) {
                    $cyclesFound[] = ['tenant_id' => $tenant->id, 'cycle' => $path];

                    // Break cycle: null out the last node's parent_id
                    $offender = end($path);

                    $foundNode = Node::find($offender);

                    $foundNode->update(['parent_id' => null]);

                    $foundNode->delete();

                    $fixesApplied[] = ['tenant_id' => $tenant->id, 'fixed_node' => $offender];
                }

                unset($parentMap); // release memory
            }, 5);
        });

        if (empty($cyclesFound)) {
            $this->info('✅ No cycles detected.');
            return 0;
        }

        foreach ($cyclesFound as $cycle) {
            $message = sprintf('Tenant %d cycle: %s', $cycle['tenant_id'], implode(' → ', $cycle['cycle']));

            Sentry::captureMessage($message);

            $this->error($message);
        }

        foreach ($fixesApplied as $fix) {
            $this->info(sprintf('🔧 Fixed tenant %d by deleting node %d', $fix['tenant_id'], $fix['fixed_node']));
        }

        $this->info('All detected cycles have been broken.');

        return 0;
    }
}
