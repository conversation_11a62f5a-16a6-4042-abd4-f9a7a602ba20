<?php

namespace App\Console\Commands;

use App\Models\Line;
use App\Models\Node;
use App\Models\Tenant;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Schema;

class ResetDemoDataCommand extends Command
{
    protected $signature = 'demo:reset';

    protected $description = 'Reset demo tenant data to initial state';

    public function handle(): void
    {
        $tenant = Tenant::demo()->first();

        if (!$tenant) {
            $this->error('Demo tenant not found!');

            return;
        }

        Schema::disableForeignKeyConstraints();

        $this->cleanupAdditionalData($tenant);

        $branch = $tenant->getMainBranch();

        $branch->nodes()->forceDelete();

        $tenant->lines()->delete();

        $demoTree = json_decode(file_get_contents(database_path('data/demo-tree.json')), true);

        $this->insertDemoTree($tenant, $branch, $demoTree);

        Schema::enableForeignKeyConstraints();

        $tenant->updateStats();
    }

    private function cleanupAdditionalData(Tenant $tenant): void
    {
        $tenant->nodeAdditions()->delete();
        $tenant->nodeChanges()->delete();
    }

    private function insertDemoTree(Tenant $tenant, $branch, $nodeData = null, $parentId = null): void
    {
        $node = Node::create([
            'tenant_id' => $branch->tenant_id,
            'branch_id' => $branch->id,
            'parent_id' => $parentId,
            'name' => $nodeData['name'],
            'is_root' => $nodeData['is_root'] ?? false,
            'gender' => $nodeData['gender'],
            'life_status' => $nodeData['life_status'],
            'bg_color' => $nodeData['gender'] === 'male' ? '#3b82f6' : '#ec4899',
            'style' => $nodeData['style'],
        ]);

        $node->descendantsAndSelf->each(fn(Node $node) => $node->updateFullName());

        if ($parentId) {
            $this->createLine($tenant, $parentId, $node->id, $nodeData['style']);
        }

        foreach ($nodeData['children'] ?? [] as $child) {
            $this->insertDemoTree($tenant, $branch, $child, $node->id);
        }
    }

    private function createLine(Tenant $tenant, $fromNodeId, $toNodeId, $toNodeStyle): void
    {
        Line::create([
            'tenant_id' => $tenant->id,
            'from_node_id' => $fromNodeId,
            'to_node_id' => $toNodeId,
            'points' => [
                [
                    'x' => $toNodeStyle['x'],
                    'y' => $toNodeStyle['y'],
                ],
            ],
        ]);
    }
}
