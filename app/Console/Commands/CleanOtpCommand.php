<?php

namespace App\Console\Commands;

use App\Models\OneTimePassword;
use Illuminate\Console\Command;

class CleanOtpCommand extends Command
{
    protected $signature = 'clean:otp';

    protected $description = 'Command description';

    public function handle(): void
    {
        $this->info('Cleaning OTPs...');

        OneTimePassword::where('created_at', '<', now()->subHour())->delete();

        $this->info('Done!');
    }
}
