<?php

namespace App\Console\Commands;

use App\Enums\Gender;
use App\Enums\LifeStatus;
use App\Models\Node;
use App\Models\Tenant;
use App\Services\ProcessNode;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;

/**
 * Import a family-tree JSON into the nodes table for a given tenant.
 *
 * USAGE
 *   php artisan family-tree:import {tenant_id} [--file=storage/app/tree.json]
 *
 * The --file path is resolved **relative to the project root** (`base_path()`),
 * so you can reference any file inside your Laravel project.
 */
class ImportFamilyTreeCommand extends Command
{
    /** @var string */
    protected $signature = 'family-tree:import
                            {tenant_id : The tenant that will own the nodes}
                            {--file=storage/app/tree.json : Path *relative to project root* of the JSON file}';

    /** @var string */
    protected $description = 'Import a hierarchical JSON file into the nodes table for one tenant.';

    protected ?Tenant $tenant = null;

    public function handle(): int
    {
        $tenantId = (int) $this->argument('tenant_id');
        $relativePath = $this->option('file');
        $absolutePath = base_path($relativePath);

        /* ────── Validation ───────────────────────────────────────────── */
        $tenant = Tenant::find($tenantId);
        if (!$tenant) {
            $this->error("❌  Tenant #{$tenantId} not found.");
            Log::warning("family-tree:import — tenant {$tenantId} not found");
            return self::FAILURE;
        }
        $this->tenant = $tenant;

        if (!is_file($absolutePath)) {
            $this->error("❌  File \"{$relativePath}\" not found beneath project root.");
            Log::warning("family-tree:import — file {$relativePath} missing");
            return self::FAILURE;
        }

        /* ────── User confirmation ───────────────────────────────────── */
        $this->line("
  Tenant   : #{$tenantId}  ({$tenant->name})
  JSON file: {$relativePath}
  Action   : Delete current nodes for this tenant, then import new tree.
        ");

        if (!$this->confirm('Proceed with the import?', false)) {
            $this->info('⏹️  Import cancelled.');
            return self::SUCCESS;
        }

        Log::info('family-tree:import — starting', [
            'tenant_id' => $tenantId,
            'file' => $relativePath,
        ]);

        /* ────── Load JSON ───────────────────────────────────────────── */
        $tree = json_decode(file_get_contents($absolutePath), true, 512, JSON_THROW_ON_ERROR);

        /* ────── Import inside single TX, with FK off ────────────────── */
        Schema::disableForeignKeyConstraints();

        DB::transaction(function () use ($tree, $tenantId) {
            // Wipe the tenant’s current nodes
            Node::where('tenant_id', $tenantId)->get()->each->delete();

            // Re-insert recursively
            $this->insertNodes($tree, null, $tenantId);
        });

        Schema::enableForeignKeyConstraints();

        Log::info('family-tree:import — completed', ['tenant_id' => $tenantId]);
        $this->info('✅  Family tree imported successfully.');
        return self::SUCCESS;
    }

    /**
     * Recursive depth-first insertion.
     *
     * @param array $nodes
     * @param int|null $parentId
     * @param int $tenantId
     */
    private function insertNodes(array $nodes, ?int $parentId, int $tenantId): void
    {
        foreach ($nodes as $node) {
            ['node' => $model] = ProcessNode::create(
                data: [
                    'tenant_id' => $tenantId,
                    'parent_id' => $parentId,
                    'name' => $node['name'],
                    'gender' => Gender::from($node['gender'] ?? Gender::Male->value)->value,
                    'life_status' => LifeStatus::from($node['life_status'] ?? LifeStatus::unknown->value)->value,
                    'is_root' => $parentId === null,
                ],
                tenant: $this->tenant
            );

            Log::debug('family-tree:import — node created', [
                'tenant_id' => $tenantId,
                'node_id' => $model->id,
                'name' => $model->name,
            ]);

            if (!empty($node['children'])) {
                $this->insertNodes($node['children'], $model->id, $tenantId);
            }
        }
    }
}
