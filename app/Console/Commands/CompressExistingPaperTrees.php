<?php

namespace App\Console\Commands;

use App\Jobs\CompressPaperTreeJob;
use App\Models\Setting;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class CompressExistingPaperTrees extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'compress:paper-trees {--dry-run : Show what would be compressed without actually doing it}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Compress existing paper tree PDF files using iLovePDF API';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Searching for existing paper tree files...');

        $paperTreeSettings = Setting::where('key', Setting::PAPER_TREE_URL)
            ->whereNotNull('value')
            ->where('value', '!=', '')
            ->get();

        if ($paperTreeSettings->isEmpty()) {
            $this->info('No paper tree files found.');
            return;
        }

        $this->info("Found {$paperTreeSettings->count()} paper tree files.");

        $compressedCount = 0;
        $skippedCount = 0;

        foreach ($paperTreeSettings as $setting) {
            $filePath = $setting->value;
            
            if (!Storage::exists($filePath)) {
                $this->warn("File not found: {$filePath}");
                $skippedCount++;
                continue;
            }

            $mimeType = Storage::mimeType($filePath);
            if ($mimeType !== 'application/pdf') {
                $this->warn("Skipping non-PDF file: {$filePath} (type: {$mimeType})");
                $skippedCount++;
                continue;
            }

            $fileSize = Storage::size($filePath);
            $formattedSize = $this->formatBytes($fileSize);

            if ($this->option('dry-run')) {
                $this->line("Would compress: {$filePath} ({$formattedSize})");
            } else {
                $this->line("Dispatching compression job for: {$filePath} ({$formattedSize})");
                CompressPaperTreeJob::dispatch($filePath);
                $compressedCount++;
            }
        }

        if ($this->option('dry-run')) {
            $this->info("Dry run completed. {$paperTreeSettings->count()} files would be processed, {$skippedCount} would be skipped.");
        } else {
            $this->info("Compression jobs dispatched for {$compressedCount} files. {$skippedCount} files were skipped.");
            $this->info("Check the queue worker logs to monitor compression progress.");
        }
    }

    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $size = $bytes;
        $unitIndex = 0;
        
        while ($size >= 1024 && $unitIndex < count($units) - 1) {
            $size /= 1024;
            $unitIndex++;
        }
        
        return round($size, 2) . ' ' . $units[$unitIndex];
    }
}
