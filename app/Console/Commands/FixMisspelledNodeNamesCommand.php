<?php

namespace App\Console\Commands;

use App\Models\Node;
use App\Services\FixMisspelledNodeNameService;
use Illuminate\Console\Command;

class FixMisspelledNodeNamesCommand extends Command
{
    protected $signature = 'fix:misspelled-node-names';

    protected $description = 'fix misspelled node names of node';

    public function handle(): void
    {
        foreach (FixMisspelledNodeNameService::NAMES as $oldName => $newName) {
            Node::where('name', $oldName)->update(['name' => $newName]);
        }
    }
}
