<?php

use App\Mail\GenericMail;
use App\Models\Admin;
use App\Models\Member;
use App\Models\Tenant;
use App\Models\User;
use GeniusTS\HijriDate\Hijri;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Exists;
use Illuminate\Validation\Rules\Unique;
use Illuminate\Validation\ValidationException;
use PostHog\PostHog;

if (!function_exists('success_msg')) {
    /**
     * @return string
     */
    function success_msg(string $resourceName, string $operation)
    {
        return " تم {$operation} {$resourceName} بنجاح ";
    }
}

if (!function_exists('tenant')) {
    function tenant(?string $url = null): ?Tenant
    {
        if ($url) {
            $url = strtolower($url);

            return Tenant::whereHas('settings', fn($query) => $query->url($url))->first();
        }

        // this is not good, will remove it when finish the mobile app
        if (request()->tenant instanceof Tenant) {
            return request()->tenant;
        }

        return request()->user()?->tenant;
    }
}

if (!function_exists('hijri_to_gregorian')) {
    function hijri_to_gregorian(string $date): ?string
    {
        $array = explode('/', $date);

        $year = $array[0];
        $month = $array[1];
        $day = $array[2];

        if (!is_numeric($year) || !is_numeric($month) || !is_numeric($day)) {
            return null;
        }

        return Hijri::convertToGregorian((int) $day, (int) $month, (int) $year)->toDateString();
    }
}

if (!function_exists('invalidate')) {
    /**
     * @throws ValidationException
     */
    function invalidate(string|array $message)
    {
        if (is_string($message)) {
            throw ValidationException::withMessages([$message]);
        }

        throw ValidationException::withMessages($message);
    }
}

if (!function_exists('ok')) {
    function ok(?string $success = null): JsonResponse
    {
        return response()->json($success ? ['success' => $success] : null);
    }
}

if (!function_exists('json')) {
    function json($data, ?string $success = null): JsonResponse
    {
        return response()->json([
            'data' => $data,
            'success' => $success,
        ]);
    }
}

if (!function_exists('tenant_unique')) {
    function tenant_unique(string $table, string $column, ?Model $model = null): Unique
    {
        if ($model) {
            return Rule::unique($table, $column)->ignoreModel($model)->where('tenant_id', tenant()->id);
        }

        return Rule::unique($table, $column)->where('tenant_id', tenant()->id);
    }
}

if (!function_exists('tenant_exists')) {
    function tenant_exists(string $table, string $column): Exists
    {
        return Rule::exists($table, $column)->where('tenant_id', tenant()->id);
    }
}

if (!function_exists('user')) {
    function user(): ?User
    {
        return auth('web')->user();
    }
}

if (!function_exists('admin')) {
    function admin(): ?Admin
    {
        return auth('admin')->user();
    }
}

if (!function_exists('member')) {
    function member(): ?Member
    {
        /** @var Member $member */
        $member = auth()->user();

        return $member instanceof Member ? $member : null;
    }
}

if (!function_exists('is_testing')) {
    function is_testing(?string $identifier = null): bool
    {
        $identifier ??= auth()->user()?->mobile;

        return $identifier === config('app.demo_mobile') || app()->isLocal();
    }
}

if (!function_exists('postHog')) {
    function postHog(array $message): void
    {
        rescue(
            fn() => PostHog::capture([
                'distinctId' => auth()->id() ?? \Illuminate\Support\Str::random(10),
                '$groups' => [
                    'tenant' => tenant()?->id,
                ],
                ...$message,
            ])
        );
    }
}

if (!function_exists('mailAdmin')) {
    function mailAdmin(string $title, string $body): void
    {
        Mail::to('<EMAIL>')->send(new GenericMail(title: $title, body: $body));
    }
}

if (!function_exists('preventEditingDemo')) {
    function preventEditingDemo(): void
    {
        if (user()->is_demo && !app()->isLocal()) {
            invalidate('لا يمكن تعديل الإعدادات في حساب النسخة التجريبية');
        }
    }
}
