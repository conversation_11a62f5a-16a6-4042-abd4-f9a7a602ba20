<?php

namespace App\Providers;

use App\Models\Admin;
use Gate;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\ServiceProvider;
use Laravel\Pulse\Facades\Pulse;
use PostHog\PostHog;
use URL;
use Vite;

class AppServiceProvider extends ServiceProvider
{
    /**
     * The path to the "home" route for your application.
     *
     * This is used by <PERSON><PERSON> authentication to redirect users after login.
     *
     * @var string
     */
    public const HOME = '/user/dashboard';

    public function register(): void
    {
        Model::unguard();
    }

    public function boot(): void
    {
        URL::forceScheme('https');

        Pulse::user(
            fn($user) => [
                'name' => $user->name,
                'extra' => $user->modile ?? $user->email,
                'avatar' => $user->avatar_url,
            ]
        );

        Gate::define('viewPulse', fn($user = null) => auth('admin')->user() instanceof Admin);

        Pulse::user(
            fn($user) => [
                'name' => $user->name,
                'extra' => "$user->email $user->mobile {$user->tenant?->name}",
            ]
        );

        if ($this->app->isProduction()) {
            PostHog::init('phc_CVPSRzgzGDrJZbYhLaMHm94BNxaQWdxBOiPe1yUr4HN', [
                'host' => 'https://us.i.posthog.com',
            ]);
        } else {
            PostHog::init(
                '123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789'
            );
        }

        Vite::prefetch();

        $this->bootAuth();
        $this->bootRoute();
    }

    public function bootAuth()
    {
        RateLimiter::for('login', fn(Request $request) => Limit::perMinute(30)->by($request->ip()));

        Gate::before(fn($user) => true);
    }

    public function bootRoute()
    {
        RateLimiter::for('api', function (Request $request) {
            return Limit::perMinute(60)->by($request->user()?->id ?: $request->ip());
        });
    }
}
