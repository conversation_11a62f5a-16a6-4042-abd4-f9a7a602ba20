<?php

namespace App\Providers;

use App\Services\ILovePdfService;
use Illuminate\Support\ServiceProvider;

class ILovePdfServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(ILovePdfService::class, function ($app) {
            return new ILovePdfService();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
