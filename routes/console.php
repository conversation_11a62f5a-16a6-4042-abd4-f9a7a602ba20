<?php

use Illuminate\Support\Facades\Schedule;

Schedule::command('backup:clean')->daily()->at('01:00');
Schedule::command('backup:run')->daily()->at('01:30');

Schedule::command('clean:otp')->daily();
Schedule::command('demo:reset')->everyThirtyMinutes();

Schedule::command('cache:prune-stale-tags')->hourly();

Schedule::command('fix:misspelled-node-names')->everyFiveMinutes();

Schedule::command('tree:detect-cycles')->daily();
