<?php

use App\Http\Controllers\Tenant\RelationshipController;
use App\Http\Controllers\WebhookController;
use App\Http\Controllers\WelcomeController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/
Route::get('', [WelcomeController::class, 'index'])->name('welcome');

// Webhook routes (outside of any middleware)
Route::post('webhooks/moyasar', [WebhookController::class, 'moyasar'])->name('webhooks.moyasar');

require __DIR__ . '/dashboard/auth.php';

Route::view('safety-standard', 'safety-standard');

Route::inertia('privacy-policy', 'PrivacyPolicy');
Route::inertia('refund-policy', 'RefundPolicy');

Route::get('records', fn() => response()->file(public_path('.well-known/assetlinks.json')));
