<?php

use App\Http\Controllers\Auth\Mobile\DemoLoginController;
use App\Http\Controllers\Auth\Mobile\GetTenantByUrlController;
use App\Http\Controllers\Auth\Mobile\GuestLoginController;
use App\Http\Controllers\Auth\Mobile\LoginController;
use App\Http\Controllers\Auth\Mobile\PasswordLoginController;
use App\Http\Controllers\Auth\Mobile\UpdateGuestNameController;
use App\Http\Controllers\Member\Mobile\PublicNodeFilterController;
use App\Http\Controllers\Member\RequestChangeEmailController;
use App\Http\Controllers\Member\RequestChangeMobileController;

Route::post('/login/guest', GuestLoginController::class);
Route::post('/guest/update-name', UpdateGuestNameController::class)->middleware('auth:sanctum');

Route::post('/login/identifier', [LoginController::class, 'identifier'])->middleware('throttle:login');
Route::post('/login/otp', [LoginController::class, 'otp'])->middleware('throttle:login');
Route::post('/login/password', PasswordLoginController::class)->middleware('throttle:login');

Route::post('/login/demo', DemoLoginController::class);

Route::post('/logout', [LoginController::class, 'logout'])->middleware('auth:sanctum');

Route::post('nodes/request-mobile-change/{node}', [RequestChangeMobileController::class, 'store']);
Route::post('nodes/request-email-change/{node}', [RequestChangeEmailController::class, 'store']);

Route::get('tenants/{url}', GetTenantByUrlController::class);
Route::get('nodes/public-filter/{url}', [PublicNodeFilterController::class, 'index']);
