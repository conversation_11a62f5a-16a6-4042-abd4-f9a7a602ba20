<?php

use App\Http\Controllers\Admin\TenantController;
use App\Http\Controllers\Auth\Admin\LoginController;

Route::get('', fn() => redirect()->route('admin.login'));

Route::prefix('auth')
    ->middleware('guest:admin')
    ->group(function () {
        Route::inertia('login', 'Admin/Auth/Login')->name('login');
        Route::post('login', [LoginController::class, 'login'])->middleware(['throttle:login']);
    });

Route::middleware('auth:admin')->group(function () {
    Route::resource('tenants', TenantController::class)->only(['index', 'destroy']);

    Route::post('tenants/login/{user}', [TenantController::class, 'login'])->name('tenants.login');
    Route::put('tenants/{tenant}', [TenantController::class, 'update'])->name('tenants.update');
});

Route::post('/logout', [LoginController::class, 'logout'])->name('logout');
