
# 🛠️ Frontend Development Guidelines

These rules must be strictly followed across all frontend development — especially in production environments like Awraq.

---

## ✅ Code Conventions

1. **Write full, clean, safe, production-ready code.**
2. **Never use placeholders** like `TODO`, `TBD`, or partial stubs.
3. **Avoid `any` in TypeScript** — use precise, strong typings.
4. **Always use named exports**, unless a default is explicitly required (e.g., Inertia pages).
5. **Use `clsx` for conditional class names**, always.
6. **Use only `@hugeicons-pro/core-stroke-standard` for icons.**
7. **Never use `@hugeicons/core-free-icons`.**
8. **Use `HugeiconsIcon` like so:**
   ```tsx
   import { HugeiconsIcon } from '@hugeicons/react';
   import { Notification03Icon } from '@hugeicons-pro/core-stroke-standard';

   <HugeiconsIcon icon={Notification03Icon} size={24} color="currentColor" strokeWidth={1.5} />
   ```

---

## ✅ Inertia.js + React

9. **Do not use `useFormContext`** — it does not exist in Inertia.js.
10. **Always pass the `form` object from `useForm()` as a typed prop.**
11. **Use `InertiaFormProps` from `@inertiajs/react`** for typing form props.
12. **Always replace:**
    ```tsx
    import Link from '@/components/Link';
    ```
    with:
    ```tsx
    import { Link } from '@inertiajs/react';
    ```
12. you should always type InertiaFormProps<any> correctly
---

## ✅ UI Libraries

13. **Use `shadcn/ui` components** for all inputs, buttons, dropdowns, etc.
14. **Use `vaul` for drawers/modals**, not `vue-spring-bottom-sheet`.
15. **Use `@react-use`’s `useMedia` for media queries** — avoid `@vueuse/core`.
16. **Do not use `lodash.pickBy`** — implement manual key filtering instead.

---

## ✅ Pagination

17. **Use a simple custom pagination component**, designed for Laravel pagination format.
18. **Do not use the shadcn pagination API abstraction.**

---

## ✅ Behavioral & Architectural Principles

19. **No shortcuts. Always fully implement logic, structure, and UI.**
20. **Double-check every line — this is a production-grade system used by thousands.**
21. **When converting from Vue to React:**
    - Preserve exact functionality and design.
    - Fully convert all components and interactions.
    - Match user experience precisely.
22. **DONT write comments unless you have to 100%**
23. When using Zustand, ONLY use it reactively - never use getState() or similar non-reactive methods.
24. Always access Zustand store state through hooks within components for proper reactivity.
---

> 💡 Stick to these principles to ensure long-term quality, performance, and maintainability across the app.