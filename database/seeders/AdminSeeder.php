<?php

namespace Database\Seeders;

use App\Models\Admin;
use Illuminate\Database\Seeder;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Admin::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'محمد الخضيري',
                'email' => '<EMAIL>',
                'password' => '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
            ]
        );

        Admin::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'بسمة',
                'email' => '<EMAIL>',
                'password' => '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
            ]
        );
        Admin::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'أيوب العايد',
                'email' => '<EMAIL>',
                'password' => '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
            ]
        );
    }
}
