<?php

namespace Database\Seeders;

use App\Models\Package;
use Illuminate\Database\Seeder;

class PackageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $packages = [
            [
                'id' => 1,
                'name' => '100 فرد',
                'nodes' => 100,
                'price' => 150,
                'savings' => null,
                'is_discounted' => false,
                'original_price' => null,
                'is_active' => true,
            ],
            [
                'id' => 2,
                'name' => '500 فرد',
                'nodes' => 500,
                'price' => 600,
                'savings' => 150,
                'is_discounted' => false,
                'original_price' => null,
                'is_active' => true,
            ],
            [
                'id' => 3,
                'name' => '1000 فرد',
                'nodes' => 1000,
                'price' => 1000,
                'savings' => 500,
                'is_discounted' => false,
                'original_price' => null,
                'is_active' => true,
            ],
            [
                'id' => 4,
                'name' => '5000 فرد',
                'nodes' => 5000,
                'price' => 4000,
                'savings' => 3500,
                'is_discounted' => false,
                'original_price' => null,
                'is_active' => true,
            ],
            [
                'id' => 5,
                'name' => '50 فرد (عرض خاص)',
                'nodes' => 50,
                'price' => 64,
                'savings' => null,
                'is_discounted' => true,
                'original_price' => 75,
                'is_active' => true,
            ],
        ];

        foreach ($packages as $package) {
            Package::updateOrCreate(['id' => $package['id']], $package);
        }
    }
}
