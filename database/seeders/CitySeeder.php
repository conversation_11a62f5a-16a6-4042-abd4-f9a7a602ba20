<?php

namespace Database\Seeders;

use App\Models\City;
use App\Models\Country;
use App\Models\District;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CitySeeder extends Seeder
{
    public function run(): void
    {
        if (City::count()) {
            return;
        }

        $now = now();

        DB::transaction(function () use ($now) {
            Country::insert(
                collect(json_decode(file_get_contents('world.json'), true))
                    ->map(
                        fn($city) => [
                            'id' => $city['id'],
                            'name' => $city['name'],
                            'created_at' => $now,
                            'updated_at' => $now,
                        ]
                    )
                    ->toArray()
            );

            City::insert(
                collect(json_decode(file_get_contents('cities.json'), true))
                    ->map(
                        fn($city) => [
                            'id' => $city['city_id'],
                            'name' => $city['name_ar'],
                            'location' => json_encode([
                                'lat' => $city['center'][0],
                                'lng' => $city['center'][1],
                            ]),
                            'created_at' => $now,
                            'updated_at' => $now,
                        ]
                    )
                    ->toArray()
            );

            District::insert(
                collect(json_decode(file_get_contents('districts_lite.json'), true))
                    ->map(
                        fn($district) => [
                            'id' => $district['district_id'],
                            'name' => $district['name_ar'],
                            'city_id' => $district['city_id'],
                            'created_at' => $now,
                            'updated_at' => $now,
                        ]
                    )
                    ->toArray()
            );

            $cities = City::all();
            $cityDupes = $cities->diff($cities->unique('name'));
            $cityDupes->toQuery()->doesntHave('districts')->delete();
        });
    }
}
