<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::disableForeignKeyConstraints();
        Schema::dropIfExists('plans');
        Schema::dropIfExists('subscriptions');
        Schema::dropIfExists('subscription_requests');
        Schema::dropIfExists('addrs');
        Schema::dropIfExists('places');
        Schema::dropIfExists('citations');
        Schema::dropIfExists('chans');
        Schema::dropIfExists('authors');
        Schema::dropIfExists('families');
        Schema::dropIfExists('family_events');
        Schema::dropIfExists('family_slgs');
        Schema::dropIfExists('media_objects');
        Schema::dropIfExists('media_objects_file');
        Schema::dropIfExists('importjobs');
        Schema::dropIfExists('model_has_permissions');
        Schema::dropIfExists('permissions');
        Schema::dropIfExists('role_has_permissions');
        Schema::dropIfExists('roles');
        Schema::dropIfExists('model_has_roles');
        Schema::dropIfExists('person_alia');
        Schema::dropIfExists('person_anci');
        Schema::dropIfExists('person_asso');
        Schema::dropIfExists('person_desi');
        Schema::dropIfExists('person_events');
        Schema::dropIfExists('person_lds');
        Schema::dropIfExists('person_name');
        Schema::dropIfExists('person_name_fone');
        Schema::dropIfExists('person_name_romn');
        Schema::dropIfExists('person_subm');
        Schema::dropIfExists('people');
        Schema::dropIfExists('publications');
        Schema::dropIfExists('refn');
        Schema::dropIfExists('subns');
        Schema::dropIfExists('subms');
        Schema::dropIfExists('source_data');
        Schema::dropIfExists('source_data_even');
        Schema::dropIfExists('source_ref');
        Schema::dropIfExists('source_repo');
        Schema::dropIfExists('sourceref_even');
        Schema::dropIfExists('sources');
        Schema::dropIfExists('repositories');
        Schema::dropIfExists('repositories');
        Schema::enableForeignKeyConstraints();
    }
};
