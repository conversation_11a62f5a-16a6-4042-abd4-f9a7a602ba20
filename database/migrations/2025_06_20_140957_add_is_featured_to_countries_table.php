<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('countries', function (Blueprint $table) {
            $table->boolean('is_featured')->default(false);
        });

        \App\Models\Country::whereIn('countries.name', [
            'السعودية',
            'الإمارات العربية المتحدة',
            'الكويت',
            'قطر',
            'البحرين',
            'عمان',
            'مصر',
            'الأردن',
            'لبنان',
            'المغرب',
        ])->update(['is_featured' => true]);
    }
};
