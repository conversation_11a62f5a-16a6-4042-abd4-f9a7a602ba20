<?php

use App\Models\Tenant;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('documents', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(Tenant::class)->index();
            $table->string('title');
            $table->text('description')->nullable();
            $table->string('file_path');
            $table->bigInteger('file_size'); // File size in bytes
            $table->string('mime_type');
            $table->string('original_filename');
            $table->string('compressed_file_path')->nullable();
            $table->enum('compression_status', ['pending', 'processing', 'completed', 'failed'])->default('pending');
            $table->json('compression_metadata')->nullable(); // Store compression details
            $table->timestamps();

            $table->index(['tenant_id', 'created_at']);
            $table->index(['compression_status']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('documents');
    }
};
