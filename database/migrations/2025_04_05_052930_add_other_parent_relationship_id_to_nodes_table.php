<?php

use App\Models\Relationship;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('nodes', function (Blueprint $table) {
            $table
                ->foreignIdFor(Relationship::class, 'other_parent_relationship_id')
                ->nullable()
                ->constrained('relationships')
                ->nullOnDelete();
        });
    }

    public function down(): void
    {
        Schema::table('nodes', function (Blueprint $table) {
            $table->dropForeignIdFor(Relationship::class, 'other_parent_relationship_id');
        });
    }
};
