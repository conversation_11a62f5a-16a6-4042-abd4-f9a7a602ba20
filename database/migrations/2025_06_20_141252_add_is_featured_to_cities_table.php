<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('cities', function (Blueprint $table) {
            $table->boolean('is_featured')->default(false);
        });

        \App\Models\City::whereIn('name', [
            'الرياض',
            'جدة',
            'مكة المكرمة',
            'المدينة المنورة',
            'الدمام',
            'الخبر',
            'الطائف',
            'بريدة',
            'تبوك',
            'خميس مشيط',
        ])->update(['is_featured' => true]);
    }
};
