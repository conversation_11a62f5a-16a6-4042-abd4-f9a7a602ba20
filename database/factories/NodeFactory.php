<?php

namespace Database\Factories;

use App\Models\Branch;
use App\Models\Tenant;
use Illuminate\Database\Eloquent\Factories\Factory;

class NodeFactory extends Factory
{
    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        $birthDate = now()->subYear();
        $tenant = Tenant::factory()->create();
        $branch = Branch::factory()->create(['tenant_id' => $tenant->id]);

        return [
            'name' => $this->faker->firstName(),
            'mobile' => $this->faker->phoneNumber(),
            'gender' => 'male',
            'life_status' => 'alive',
            'birth_date' => $birthDate,
            'death_date' => $birthDate->addYears(50),
            'tenant_id' => $tenant->id,
            'branch_id' => $branch->id,
            'full_name' => $this->faker->name(),
            'is_root' => false,
            'bg_color' => '#000000',
        ];
    }
}
