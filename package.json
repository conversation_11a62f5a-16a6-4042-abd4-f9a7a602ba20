{"private": true, "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint --ext .js,.tsx,.ts resources --fix", "format": "prettier --write .", "convert:webp": "node scripts/convert-to-webp.js"}, "dependencies": {"@headlessui/react": "^2.2.4", "@hugeicons-pro/core-stroke-standard": "^1.0.14", "@hugeicons/react": "^1.0.5", "@inertiajs/react": "^2.0.13", "@midudev/tailwind-animations": "^0.2.0", "@popperjs/core": "^2.11.8", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-direction": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@sentry/react": "^9.30.0", "@sentry/vite-plugin": "^3.5.0", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.10", "@tanstack/react-query": "^5.81.0", "@tanstack/react-query-persist-client": "^5.81.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "@xyflow/react": "^12.7.0", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "concurrently": "^9.1.2", "d3": "^7.9.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "elkjs": "^0.10.0", "entitree-flex": "^0.4.1", "hijri-js": "^1.0.25", "idb-keyval": "^6.2.2", "konva": "^9.3.20", "laravel-vite-plugin": "^1.3.0", "lucide-react": "^0.511.0", "motion-dom": "12.5.0", "motion-v": "^1.3.0", "next-themes": "^0.4.6", "nuqs": "^2.4.3", "posthog-js": "^1.255.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-error-boundary": "^6.0.0", "react-fast-memo": "^2.0.1", "react-medium-image-zoom": "^5.2.14", "react-scan": "^0.3.4", "react-select": "^5.10.1", "react-use": "^17.6.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.10", "tailwindcss-animate": "^1.0.7", "tiny-emitter": "^2.1.0", "use-debounce": "^10.0.5", "vaul": "^1.1.2", "vite": "npm:rolldown-vite@latest", "zustand": "^5.0.5"}, "type": "module", "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.29.0", "@prettier/plugin-php": "^0.22.4", "@tanstack/eslint-plugin-query": "^5.81.0", "@types/d3": "^7.4.3", "@types/node": "^22.15.32", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "globals": "^16.2.0", "prettier": "^3.5.3", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.13", "typescript": "^5.8.3", "typescript-eslint": "^8.34.1"}}