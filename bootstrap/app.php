<?php

use App\Http\Controllers\FamilyHomeController;
use App\Http\Middleware\Authenticate;
use App\Http\Middleware\HandleInertiaRequests;
use App\Http\Middleware\InsideTenant;
use App\Http\Middleware\ProcessTenantFromDomain;
use App\Http\Middleware\RedirectIfAuthenticated;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Sentry\Laravel\Integration;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        commands: __DIR__ . '/../routes/console.php',
        then: function () {
            Route::middleware(['web', 'process-tenant-from-domain'])
                ->domain('{url}.' . config('app.url'))
                ->group(function () {
                    Route::get('/', FamilyHomeController::class);
                });

            Route::middleware('web')->group(base_path('routes/web.php'));

            Route::middleware(['web', 'auth:sanctum', 'inside-tenant'])
                ->prefix('user')
                ->group(base_path('routes/dashboard/tenant.php'));

            Route::middleware('web')->name('admin.')->prefix('admin')->group(base_path('routes/admin.php'));

            Route::middleware('api')
                ->prefix('api/v1/mobile')
                ->group(function () {
                    require base_path('routes/mobile/auth.php');

                    Route::middleware('auth:sanctum')->group(base_path('routes/mobile/mobile.php'));
                });
        }
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->web(HandleInertiaRequests::class);

        $middleware->api('throttle:api', SubstituteBindings::class);

        $middleware->alias([
            'auth' => Authenticate::class,
            'guest' => RedirectIfAuthenticated::class,
            'inside-tenant' => InsideTenant::class,
            'process-tenant-from-domain' => ProcessTenantFromDomain::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        Integration::handles($exceptions);
    })
    ->create();
